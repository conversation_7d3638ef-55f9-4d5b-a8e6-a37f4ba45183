Criar Saque
POST
https://api.pagdrop.com/v1/transfers

Através dessa rota POST /transfers você pode solicitar um saque.
Observações:
1. É necessário utilizar o header x-withdraw-key contendo a chave de saque externo.
2. Para que essa rota funcione, a funcionalidade de saque via API deve estar habilitada.
3. Certifique-se que a validação de titularidade esteja desativada para saques em nome de terceiros.

Header Params
x-withdraw-keystringrequired
Chave de saque externo vinculada ao usuário.

Body Params
methodstringrequired
Define qual será o método de transferência do valor solicitado.
fiat ou crypto


amountint32required
Valor em centavos do saque. (Ex: 100 = R$1,00).

netPayoutboolean
Define se a taxa será descontada do valor solicitado (false) ou adicionada ao total (true).


Selecione...
pixKeystringrequired
Chave PIX usada para receber a transferência.

pixKeyTypestringrequired
Tipo da chave PIX (CPF, CNPJ, e-mail, telefone, aleatória ou Copia e cola).



CREDENCIAIS
Basic
sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
:
pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc

GERAR O 

"authorization: Basic"

"x-withdraw-key: wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m"


<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/transfers",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => "{\n  \"method\": \"fiat\",\n  \"amount\": 100,\n  \"netPayout\": false,\n  \"pixKey\": \"63999288544\",\n  \"pixKeyType\": \"phone\"\n}",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw==",
    "content-type: application/json",
    "x-withdraw-key: wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}

RESPONSE 200 REAL DA API


{
  "id": 165595,
  "tenantId": "7a45cd92-63f7-4e31-92ed-5ef13b9333fa",
  "companyId": 17696,
  "amount": 100,
  "netAmount": 98,
  "currency": "BRL",
  "fee": 2,
  "method": "fiat",
  "status": "PENDING_QUEUE",
  "externalRef": null,
  "isExternal": true,
  "pixKey": "63999288544",
  "pixKeyType": "phone",
  "pixEnd2EndId": null,
  "cryptoWallet": null,
  "cryptoNetwork": null,
  "cryptoAddress": null,
  "description": "SENDING WITHDRAW REQUEST",
  "metadata": "{\n  \"isExternal\": true,\n  \"secretKey\": true,\n  \"withdrawKey\": true,\n  \"systemKey\": false,\n  \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n  \"hostname\": \"api.pagdrop.com\",\n  \"ip\": \"*************\",\n  \"request\": {\n    \"method\": \"fiat\",\n    \"amount\": 100,\n    \"netPayout\": false,\n    \"pixKey\": \"63999288544\",\n    \"pixKeyType\": \"phone\"\n  }\n}",
  "postbackUrl": null,
  "history": [],
  "transferredAt": null,
  "processedAt": null,
  "canceledAt": null,
  "createdAt": "2025-07-31T02:14:16.091Z",
  "updatedAt": "2025-07-31T02:14:16.091Z"
}



OUTROS RETORNO

{
  "message": "Valor do saque é menor que o limite de permitido (R$ 1,00)"
}


INFORMACAO RESPONSES

200
Objeto de resposta em caso de sucesso na criação do saque

400
Objeto de resposta em caso de falha
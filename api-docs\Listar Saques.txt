Listar Saques
GET
https://api.pagdrop.com/v1/transfers/

A rota GET /transferspermite listar todos os seus saques.

Header Params
x-withdraw-keystringrequired
Chave de saque externo vinculada ao usuário.

Query Params
pageint32
Número da página dos resultados a ser retornada. Este parâmetro permite a navegação entre páginas de registros. O valor padrão é 1, o que retorna a primeira página de dados.

pageSizeint32
Quantidade de itens a serem exibidos por página. Define o limite de registros retornados em uma única página de resultados. O valor padrão é 20, e o valor máximo permitido é 50. Caso um valor maior que 50 seja especificado, ele será automaticamente ajustado para o limite máximo.

CREDENCIAIS
Basic
sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
:
pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc



<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/transfers?page=1&pageSize=50",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "GET",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw==",
    "x-withdraw-key: wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}

RESPONSE REAL DA API
200

{
  "pagination": {
    "page": 1,
    "pageSize": 50,
    "totalRecords": 3,
    "totalPages": 1
  },
  "data": [
    {
      "id": 165595,
      "tenantId": "7a45cd92-63f7-4e31-92ed-5ef13b9333fa",
      "companyId": 17696,
      "amount": 100,
      "netAmount": 98,
      "currency": "BRL",
      "fee": 2,
      "method": "fiat",
      "status": "PROCESSING",
      "externalRef": null,
      "isExternal": true,
      "pixKey": "63999288544",
      "pixKeyType": "phone",
      "pixEnd2EndId": null,
      "cryptoWallet": null,
      "cryptoNetwork": null,
      "cryptoAddress": null,
      "description": "FAILED: WAITING RETRY",
      "metadata": "{\n  \"isExternal\": true,\n  \"secretKey\": true,\n  \"withdrawKey\": true,\n  \"systemKey\": false,\n  \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n  \"hostname\": \"api.pagdrop.com\",\n  \"ip\": \"*************\",\n  \"request\": {\n    \"method\": \"fiat\",\n    \"amount\": 100,\n    \"netPayout\": false,\n    \"pixKey\": \"63999288544\",\n    \"pixKeyType\": \"phone\"\n  }\n}",
      "postbackUrl": null,
      "history": [
        {
          "date": "2025-07-31T02:14:29.904Z",
          "message": "Request failed with status code 500"
        }
      ],
      "transferredAt": null,
      "processedAt": null,
      "canceledAt": null,
      "createdAt": "2025-07-31T02:14:16.091Z",
      "updatedAt": "2025-07-31T02:14:29.908Z"
    },
    {
      "id": 164944,
      "tenantId": "7a45cd92-63f7-4e31-92ed-5ef13b9333fa",
      "companyId": 17696,
      "amount": 3360,
      "netAmount": 3292,
      "currency": "BRL",
      "fee": 67,
      "method": "fiat",
      "status": "COMPLETED",
      "externalRef": null,
      "isExternal": true,
      "pixKey": "5f67db6d-b721-4804-ba9d-6ce55bc43537",
      "pixKeyType": "evp",
      "pixEnd2EndId": null,
      "cryptoWallet": null,
      "cryptoNetwork": null,
      "cryptoAddress": null,
      "description": "PAID",
      "metadata": "{\n  \"metadata\": \"{\\\"userId\\\":17696,\\\"sessionToken\\\":\\\"66294cba-3a82-4478-a578-777b6692e7b0\\\",\\\"loginAsAdmin\\\":false}\",\n  \"isExternal\": false,\n  \"secretKey\": true,\n  \"withdrawKey\": false,\n  \"systemKey\": true,\n  \"userAgent\": \"axios/1.7.7\",\n  \"hostname\": \"api.pagdrop.com\",\n  \"ip\": \"************\",\n  \"request\": {\n    \"method\": \"fiat\",\n    \"amount\": 3360,\n    \"pixKey\": \"5f67db6d-b721-4804-ba9d-6ce55bc43537\",\n    \"pixKeyType\": \"evp\",\n    \"metadata\": \"{\\\"userId\\\":17696,\\\"sessionToken\\\":\\\"66294cba-3a82-4478-a578-777b6692e7b0\\\",\\\"loginAsAdmin\\\":false}\",\n    \"netPayout\": false\n  }\n}",
      "postbackUrl": null,
      "history": [],
      "transferredAt": "2025-07-30T14:57:20.673Z",
      "processedAt": "2025-07-30T14:57:17.085Z",
      "canceledAt": null,
      "createdAt": "2025-07-30T14:28:17.775Z",
      "updatedAt": "2025-07-30T14:57:20.674Z"
    },
    {
      "id": 161178,
      "tenantId": "7a45cd92-63f7-4e31-92ed-5ef13b9333fa",
      "companyId": 17696,
      "amount": 1000,
      "netAmount": 594,
      "currency": "BRL",
      "fee": 406,
      "method": "fiat",
      "status": "COMPLETED",
      "externalRef": null,
      "isExternal": true,
      "pixKey": "60379605000167",
      "pixKeyType": "cnpj",
      "pixEnd2EndId": null,
      "cryptoWallet": null,
      "cryptoNetwork": null,
      "cryptoAddress": null,
      "description": "PAID",
      "metadata": "{\n  \"metadata\": \"{\\\"userId\\\":17696,\\\"sessionToken\\\":\\\"924bc11e-dd94-40c7-8807-398046580dd2\\\",\\\"loginAsAdmin\\\":false}\",\n  \"isExternal\": false,\n  \"secretKey\": true,\n  \"withdrawKey\": false,\n  \"systemKey\": true,\n  \"userAgent\": \"axios/1.7.7\",\n  \"hostname\": \"api.pagdrop.com\",\n  \"ip\": \"************\",\n  \"request\": {\n    \"method\": \"fiat\",\n    \"amount\": 1000,\n    \"pixKey\": \"60379605000167\",\n    \"pixKeyType\": \"cnpj\",\n    \"metadata\": \"{\\\"userId\\\":17696,\\\"sessionToken\\\":\\\"924bc11e-dd94-40c7-8807-398046580dd2\\\",\\\"loginAsAdmin\\\":false}\",\n    \"netPayout\": false\n  }\n}",
      "postbackUrl": null,
      "history": [],
      "transferredAt": "2025-07-25T21:24:43.520Z",
      "processedAt": "2025-07-25T21:24:39.875Z",
      "canceledAt": null,
      "createdAt": "2025-07-25T21:24:24.715Z",
      "updatedAt": "2025-07-25T21:24:43.521Z"
    }
  ]
}




INFORMACAO RESPONSES

200
Objeto de resposta contendo a lista de saques com paginação

400
Objeto de resposta em caso de falha




























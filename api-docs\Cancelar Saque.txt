Cancelar Saque
POST
https://api.pagdrop.com/v1/transfers/{id}/cancel

Através dessa rota POST transfers/{id}/cancel você pode cancelar um saque.
Observações:
1. É necessário utilizar o header x-withdraw-key contendo a chave de saque externo.
2. Para que essa rota funcione, a funcionalidade de saque via API deve estar habilitada.


Header Params
x-withdraw-keystringrequired
Chave de saque externo vinculada ao usuário.

Path Params
idint32required
ID do saque a ser cancelado.



CREDENCIAIS
Basic
sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc


x-withdraw-key string required
Chave de saque externo vinculada ao usuário.
wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m


<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/transfers/165595/cancel",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw==",
    "content-type: application/json",
    "x-withdraw-key: wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}


RESPONSE REAL DA API
400

{
  "message": "Saque não está aguardando análise."
}


INFORMACAO DE RESPONSES

200
Objeto de resposta em caso de sucesso no cancelamento de um saque

400
Objeto de resposta em caso de falha
<?php
$currentPage = basename($_SERVER['PHP_SELF']);
$currentDir = basename(dirname($_SERVER['PHP_SELF']));

function isActive($page, $dir = '') {
    global $currentPage, $currentDir;
    if ($dir && $currentDir !== $dir) return '';
    return $currentPage === $page ? 'active' : '';
}

function isActiveDir($dir) {
    global $currentDir;
    return $currentDir === $dir ? 'active' : '';
}
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo isActive('dashboard.php'); ?>" href="<?php echo BASE_URL; ?>dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Gestão de Clientes</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'users') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/users.php">
                    <i class="fas fa-users me-2"></i>
                    Clientes
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'gateway') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/gateway_configs.php">
                    <i class="fas fa-plug me-2"></i>
                    Configurações Gateway
                </a>
            </li>
            
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Transações</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'transactions') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/transactions.php">
                    <i class="fas fa-credit-card me-2"></i>
                    Transações
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'transfers') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/transfers.php">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Transferências
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'fees') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/fees.php">
                    <i class="fas fa-percentage me-2"></i>
                    Taxas Administrativas
                </a>
            </li>
            
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Sistema</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'settings') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/settings.php">
                    <i class="fas fa-cog me-2"></i>
                    Configurações
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'logs') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/logs.php">
                    <i class="fas fa-list-alt me-2"></i>
                    Logs do Sistema
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'backup') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/backup.php">
                    <i class="fas fa-database me-2"></i>
                    Backup & Restore
                </a>
            </li>
            
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Relatórios</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'reports') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/reports.php">
                    <i class="fas fa-chart-bar me-2"></i>
                    Relatórios
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveDir('admin') && strpos($currentPage, 'analytics') !== false ? 'active' : ''; ?>" 
                   href="<?php echo BASE_URL; ?>admin/analytics.php">
                    <i class="fas fa-chart-line me-2"></i>
                    Analytics
                </a>
            </li>
        </ul>
        
        <hr class="my-3">
        
        <!-- Informações do Sistema -->
        <div class="px-3">
            <h6 class="sidebar-heading text-muted">
                <span>Sistema</span>
            </h6>
            <div class="small text-muted">
                <div class="mb-1">
                    <i class="fas fa-info-circle me-1"></i>
                    Versão: <?php echo SYSTEM_VERSION; ?>
                </div>
                <div class="mb-1">
                    <i class="fas fa-server me-1"></i>
                    Status: <span class="text-success">Online</span>
                </div>
                <div class="mb-1">
                    <i class="fas fa-clock me-1"></i>
                    <?php echo date('d/m/Y H:i'); ?>
                </div>
            </div>
        </div>
        
        <hr class="my-3">
        
        <!-- Ações Rápidas -->
        <div class="px-3">
            <h6 class="sidebar-heading text-muted">
                <span>Ações Rápidas</span>
            </h6>
            <div class="d-grid gap-2">
                <a href="<?php echo BASE_URL; ?>admin/users.php?action=create" class="btn btn-sm btn-success">
                    <i class="fas fa-user-plus me-1"></i>
                    Novo Cliente
                </a>
                <a href="<?php echo BASE_URL; ?>admin/test_api.php" class="btn btn-sm btn-info">
                    <i class="fas fa-vial me-1"></i>
                    Testar API
                </a>
                <button class="btn btn-sm btn-warning" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Atualizar
                </button>
            </div>
        </div>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.15);
    border-right: 3px solid #007bff;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

.sidebar hr {
    margin: 1rem 0;
    border-color: rgba(0, 0, 0, 0.1);
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 56px;
    }
}

/* Scrollbar personalizada */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

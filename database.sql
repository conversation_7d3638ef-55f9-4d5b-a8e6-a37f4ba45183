-- Sistema Universal de Integração com Gateways de Pagamento
-- Banco de dados para XAMPP/phpMyAdmin

CREATE DATABASE IF NOT EXISTS `gateway_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `gateway_system`;

-- Ta<PERSON><PERSON> de usuários (admin e clientes)
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `user_type` enum('admin','client') NOT NULL DEFAULT 'client',
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela de configurações de gateway por cliente
CREATE TABLE `gateway_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `gateway_name` varchar(50) NOT NULL DEFAULT 'PagDrop',
  `api_base_url` varchar(255) NOT NULL,
  `public_key` varchar(255) NOT NULL,
  `secret_key` varchar(255) NOT NULL,
  `withdraw_key` varchar(255) DEFAULT NULL,
  `admin_fee_percentage` decimal(5,2) NOT NULL DEFAULT 10.00,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela de configurações do sistema
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text,
  `description` varchar(255) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela de transações
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `gateway_transaction_id` varchar(100) DEFAULT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'BRL',
  `payment_method` varchar(50) NOT NULL,
  `status` varchar(50) NOT NULL,
  `customer_name` varchar(100) DEFAULT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `customer_document` varchar(20) DEFAULT NULL,
  `admin_fee_amount` decimal(15,2) DEFAULT 0.00,
  `admin_fee_percentage` decimal(5,2) DEFAULT 0.00,
  `net_amount` decimal(15,2) DEFAULT 0.00,
  `gateway_response` text,
  `pix_qrcode` text,
  `pix_expiration` datetime DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_gateway_transaction_id` (`gateway_transaction_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela de saques/transferências
CREATE TABLE `transfers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `gateway_transfer_id` varchar(100) DEFAULT NULL,
  `amount` decimal(15,2) NOT NULL,
  `net_amount` decimal(15,2) NOT NULL,
  `fee` decimal(15,2) NOT NULL DEFAULT 0.00,
  `currency` varchar(3) NOT NULL DEFAULT 'BRL',
  `method` enum('fiat','crypto') NOT NULL DEFAULT 'fiat',
  `status` varchar(50) NOT NULL,
  `pix_key` varchar(255) DEFAULT NULL,
  `pix_key_type` varchar(20) DEFAULT NULL,
  `crypto_wallet` varchar(100) DEFAULT NULL,
  `crypto_network` varchar(50) DEFAULT NULL,
  `crypto_address` varchar(255) DEFAULT NULL,
  `description` text,
  `gateway_response` text,
  `transferred_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_gateway_transfer_id` (`gateway_transfer_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela de logs do sistema
CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `data` text,
  `level` enum('info','warning','error','critical') NOT NULL DEFAULT 'info',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  INDEX `idx_action` (`action`),
  INDEX `idx_level` (`level`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela de taxas automáticas enviadas
CREATE TABLE `admin_fees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `original_amount` decimal(15,2) NOT NULL,
  `fee_percentage` decimal(5,2) NOT NULL,
  `fee_amount` decimal(15,2) NOT NULL,
  `pix_key_destination` varchar(255) NOT NULL,
  `pix_status` enum('pending','sent','failed') NOT NULL DEFAULT 'pending',
  `pix_response` text,
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`transaction_id`) REFERENCES `transactions`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Inserir usuário admin padrão (senha: password)
INSERT INTO `users` (`username`, `email`, `password`, `full_name`, `user_type`)
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrador', 'admin');

-- Inserir usuário cliente de exemplo (senha: password)
INSERT INTO `users` (`username`, `email`, `password`, `full_name`, `user_type`)
VALUES ('cliente1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Cliente Exemplo', 'client');

-- Inserir configurações padrão do sistema
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `description`) VALUES
('admin_pix_key', '<EMAIL>', 'Chave PIX para recebimento das taxas administrativas'),
('admin_pix_key_type', 'email', 'Tipo da chave PIX (email, cpf, cnpj, phone, evp)'),
('default_gateway_name', 'PagDrop', 'Nome padrão do gateway'),
('default_api_base_url', 'https://api.pagdrop.com', 'URL base padrão da API'),
('system_name', 'Gateway Universal', 'Nome do sistema'),
('system_version', '1.0.0', 'Versão do sistema');

-- Inserir configuração de gateway para o cliente de exemplo
INSERT INTO `gateway_configs` (`user_id`, `gateway_name`, `api_base_url`, `public_key`, `secret_key`, `withdraw_key`, `admin_fee_percentage`, `status`) VALUES
(2, 'PagDrop', 'https://api.pagdrop.com', 'pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc', 'sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S', 'wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m', 10.00, 'active');

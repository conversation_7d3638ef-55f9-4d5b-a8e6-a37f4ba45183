# Sistema Universal de Integração com Gateways de Pagamento

Sistema completo para integração com múltiplos gateways de pagamento, desenvolvido especificamente para funcionar com a API PagDrop e estruturas similares.

## 🚀 Características Principais

- **Universal**: Funciona com qualquer gateway que tenha estrutura similar à PagDrop
- **Taxas Automáticas**: Sistema de cobrança automática de taxas via PIX
- **Multi-usuário**: Suporte a administradores e clientes
- **Seguro**: Autenticação robusta e logs completos
- **Responsivo**: Interface moderna e otimizada

## 📋 Pré-requisitos

- **XAMPP** instalado no Windows
- **PHP 7.4+**
- **MySQL 5.7+**
- **phpMyAdmin** (incluído no XAMPP)

## 🛠️ Instalação

### 1. Preparar o Ambiente

1. Instale o XAMPP
2. Inicie os serviços Apache e MySQL
3. Extraia os arquivos do sistema na pasta `c:\xampp\htdocs\novo\`

### 2. Configurar o Banco de Dados

1. Acesse o phpMyAdmin: `http://localhost/phpmyadmin`
2. Importe o arquivo `database.sql`
3. O banco `gateway_system` será criado automaticamente

### 3. Configuração Inicial

1. Acesse: `http://localhost/novo/setup_demo.php`
2. Execute o script de configuração inicial
3. Anote as credenciais de login fornecidas

### 4. Primeiro Acesso

**Administrador:**
- Usuário: `admin`
- Senha: `password`
- URL: `http://localhost/novo/login.php`

**Cliente de Exemplo:**
- Usuário: `cliente1`
- Senha: `password`
- URL: `http://localhost/novo/login.php`

## ⚙️ Configuração da API

### Credenciais Incluídas (Exemplo)

O sistema vem pré-configurado com as credenciais de exemplo da documentação PagDrop:

```
Chave Pública: pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc
Chave Privada: sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
Chave de Saque: wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m
```

### Para Usar Credenciais Reais

1. Faça login como administrador
2. Vá em **Gerenciar Usuários**
3. Edite o cliente ou crie um novo
4. Insira suas credenciais reais da PagDrop

## 💰 Sistema de Taxas

### Como Funciona

1. Cliente recebe um pagamento
2. Sistema calcula automaticamente a taxa configurada (ex: 10%)
3. Taxa é enviada via PIX para a chave configurada no admin
4. Valor líquido fica disponível para o cliente

### Configurar Chave PIX

1. Login como admin
2. **Configurações** → **Taxa Administrativa**
3. Configure sua chave PIX real
4. Defina o tipo da chave (email, CPF, etc.)

## 📱 Funcionalidades

### Painel Administrativo

- ✅ Dashboard com estatísticas
- ✅ Gerenciar clientes
- ✅ Configurar credenciais por cliente
- ✅ Definir taxas personalizadas
- ✅ Logs completos do sistema
- ✅ Relatórios de transações
- ✅ Configurações globais

### Painel do Cliente

- ✅ Dashboard pessoal
- ✅ Visualizar saldo disponível
- ✅ Histórico de transações
- ✅ Solicitar saques
- ✅ Simular saques crypto
- ✅ Gerar PIX para recebimento

### APIs Disponíveis

- ✅ Obter saldo disponível
- ✅ Criar transações/vendas
- ✅ Listar transações
- ✅ Criar transferências/saques
- ✅ Simular saques crypto
- ✅ Verificar status em tempo real

## 🔧 Estrutura de Arquivos

```
novo/
├── config/
│   └── config.php              # Configurações principais
├── classes/
│   ├── Database.php            # Conexão com banco
│   ├── Auth.php                # Autenticação
│   ├── GatewayAPI.php          # Integração com API
│   ├── AdminFeeManager.php     # Taxas automáticas
│   └── Logger.php              # Sistema de logs
├── admin/
│   ├── users.php               # Gerenciar usuários
│   └── settings.php            # Configurações
├── client/
│   └── dashboard.php           # Painel do cliente
├── api/
│   ├── quick_balance.php       # API de saldo
│   └── simulate_crypto.php     # Simulação crypto
├── assets/
│   ├── css/                    # Estilos
│   └── js/                     # JavaScript
├── includes/
│   ├── admin_header.php        # Header admin
│   └── client_header.php       # Header cliente
├── database.sql               # Estrutura do banco
├── login.php                  # Página de login
└── setup_demo.php            # Configuração inicial
```

## 🔒 Segurança

- **Autenticação**: Sistema robusto com sessões seguras
- **CSRF Protection**: Tokens de segurança em formulários
- **SQL Injection**: Prepared statements em todas as queries
- **Logs**: Registro completo de todas as ações
- **Validação**: Sanitização de todas as entradas

## 📊 Monitoramento

### Logs do Sistema

- Todas as ações são registradas
- Logs de API calls
- Logs de taxas enviadas
- Logs de erros e falhas

### Acesso aos Logs

1. Login como admin
2. **Logs do Sistema**
3. Filtrar por data, usuário, ação, etc.

## 🚨 Solução de Problemas

### Erro de Conexão com API

1. Verifique as credenciais no painel admin
2. Confirme se a URL da API está correta
3. Teste a conectividade com `admin/test_api.php`

### Saldo Não Aparece

1. Verifique se o cliente tem configuração de gateway
2. Confirme se as credenciais estão corretas
3. Verifique os logs de erro

### Taxa Não Enviada

1. Confirme se a chave PIX está configurada
2. Verifique se o cliente tem taxa configurada
3. Consulte os logs de taxas administrativas

## 🔄 Atualizações

Para atualizar o sistema:

1. Faça backup do banco de dados
2. Substitua os arquivos (exceto `config/config.php`)
3. Execute migrações se necessário
4. Teste todas as funcionalidades

## 📞 Suporte

Para suporte técnico:

1. Verifique os logs do sistema
2. Consulte a documentação da API PagDrop
3. Teste com as credenciais de exemplo primeiro

## 📄 Licença

Sistema desenvolvido para uso interno. Todos os direitos reservados.

---

**⚠️ Importante**: Remova o arquivo `setup_demo.php` após a configuração inicial por segurança.

**🔐 Segurança**: Altere as senhas padrão antes de usar em produção.

**💡 Dica**: Use sempre credenciais reais da PagDrop para testes em produção.

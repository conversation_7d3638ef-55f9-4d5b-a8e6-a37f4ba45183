<?php
require_once '../config/config.php';

$auth = new Auth();
$auth->requireLogin();

// Verificar se é cliente
if (!$auth->isClient()) {
    header('Location: ../dashboard.php');
    exit;
}

$user = $auth->getUser();
$userId = $auth->getUserId();

$db = Database::getInstance();

try {
    // Inicializar API do gateway
    $gatewayAPI = new GatewayAPI($userId);
    
    // Obter saldo
    $balance = $gatewayAPI->getBalance();
    
    // Obter estatísticas do cliente
    $stats = [
        'total_transactions' => $db->count('transactions', 'user_id = :user_id', ['user_id' => $userId]),
        'paid_transactions' => $db->count('transactions', 'user_id = :user_id AND status IN ("paid", "approved")', ['user_id' => $userId]),
        'total_transfers' => $db->count('transfers', 'user_id = :user_id', ['user_id' => $userId]),
        'completed_transfers' => $db->count('transfers', 'user_id = :user_id AND status = "COMPLETED"', ['user_id' => $userId])
    ];
    
    // Transações recentes
    $recentTransactions = $db->fetchAll("
        SELECT * FROM transactions 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC 
        LIMIT 5
    ", ['user_id' => $userId]);
    
    // Transferências recentes
    $recentTransfers = $db->fetchAll("
        SELECT * FROM transfers 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC 
        LIMIT 5
    ", ['user_id' => $userId]);
    
    $apiError = null;
    
} catch (Exception $e) {
    $apiError = $e->getMessage();
    $balance = null;
    $stats = [
        'total_transactions' => 0,
        'paid_transactions' => 0,
        'total_transfers' => 0,
        'completed_transfers' => 0
    ];
    $recentTransactions = [];
    $recentTransfers = [];
}

$pageTitle = 'Dashboard do Cliente';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/client.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/client_header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/client_sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Atualizar
                            </button>
                        </div>
                    </div>
                </div>

                <?php if ($apiError): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Aviso:</strong> Não foi possível conectar com a API do gateway. 
                    Algumas informações podem estar desatualizadas.
                    <br><small>Erro: <?php echo htmlspecialchars($apiError); ?></small>
                </div>
                <?php endif; ?>

                <!-- Saldo Disponível -->
                <?php if ($balance): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-left-success shadow">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Saldo Disponível
                                        </div>
                                        <div class="h4 mb-0 font-weight-bold text-gray-800">
                                            R$ <?php echo formatMoney(centavosToReais($balance['amount'])); ?>
                                        </div>
                                        <?php if (isset($balance['waitingFunds']) && $balance['waitingFunds'] > 0): ?>
                                        <div class="text-xs text-muted">
                                            Aguardando: R$ <?php echo formatMoney(centavosToReais($balance['waitingFunds'])); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-wallet fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Cards de Estatísticas -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Total Transações
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_transactions']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Transações Pagas
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['paid_transactions']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Total Saques
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_transfers']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Saques Concluídos
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['completed_transfers']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ações Rápidas -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-bolt me-2"></i>Ações Rápidas
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <a href="create_transaction.php" class="btn btn-success w-100">
                                            <i class="fas fa-plus me-2"></i>Nova Transação
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="create_transfer.php" class="btn btn-warning w-100">
                                            <i class="fas fa-money-bill-wave me-2"></i>Solicitar Saque
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="transactions.php" class="btn btn-info w-100">
                                            <i class="fas fa-list me-2"></i>Ver Transações
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="transfers.php" class="btn btn-primary w-100">
                                            <i class="fas fa-exchange-alt me-2"></i>Ver Saques
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transações e Transferências Recentes -->
                <div class="row">
                    <!-- Transações Recentes -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-credit-card me-2"></i>Transações Recentes
                                </h6>
                                <a href="transactions.php" class="btn btn-sm btn-primary">Ver Todas</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recentTransactions)): ?>
                                    <p class="text-muted text-center">Nenhuma transação encontrada</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Valor</th>
                                                    <th>Status</th>
                                                    <th>Data</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recentTransactions as $transaction): ?>
                                                <tr>
                                                    <td>#<?php echo $transaction['id']; ?></td>
                                                    <td>R$ <?php echo formatMoney($transaction['amount']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo getStatusColor($transaction['status']); ?>">
                                                            <?php echo ucfirst($transaction['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('d/m/Y H:i', strtotime($transaction['created_at'])); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Transferências Recentes -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-exchange-alt me-2"></i>Saques Recentes
                                </h6>
                                <a href="transfers.php" class="btn btn-sm btn-primary">Ver Todos</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recentTransfers)): ?>
                                    <p class="text-muted text-center">Nenhum saque encontrado</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Valor</th>
                                                    <th>Status</th>
                                                    <th>Data</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recentTransfers as $transfer): ?>
                                                <tr>
                                                    <td>#<?php echo $transfer['id']; ?></td>
                                                    <td>R$ <?php echo formatMoney($transfer['amount']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo getStatusColor($transfer['status']); ?>">
                                                            <?php echo ucfirst($transfer['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('d/m/Y H:i', strtotime($transfer['created_at'])); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/client.js"></script>
</body>
</html>

<?php
function getStatusColor($status) {
    $colors = [
        'paid' => 'success',
        'approved' => 'success',
        'waiting_payment' => 'warning',
        'pending' => 'warning',
        'refused' => 'danger',
        'cancelled' => 'secondary',
        'COMPLETED' => 'success',
        'PROCESSING' => 'warning',
        'PENDING_QUEUE' => 'info',
        'CANCELLED' => 'secondary',
        'REFUSED' => 'danger'
    ];
    
    return $colors[$status] ?? 'secondary';
}
?>

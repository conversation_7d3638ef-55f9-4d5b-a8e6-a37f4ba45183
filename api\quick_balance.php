<?php
/**
 * API para obter saldo rápido
 * Sistema Universal de Gateways
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();

// Verificar se está logado
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit;
}

// Verificar se é cliente
if (!$auth->isClient()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acesso negado']);
    exit;
}

try {
    $userId = $auth->getUserId();
    
    // Inicializar API do gateway
    $gatewayAPI = new GatewayAPI($userId);
    
    // Obter saldo
    $balance = $gatewayAPI->getBalance();
    
    // Formatar saldo
    $formattedBalance = formatMoney(centavosToReais($balance['amount']));
    
    echo json_encode([
        'success' => true,
        'balance' => $formattedBalance,
        'raw_amount' => $balance['amount'],
        'waiting_funds' => $balance['waitingFunds'] ?? 0
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao obter saldo',
        'error' => $e->getMessage()
    ]);
}
?>

<?php
/**
 * Classe de Log do Sistema
 * Sistema Universal de Gateways
 */

class Logger {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Registrar log no sistema
     */
    public function log($level, $action, $data = [], $userId = null) {
        try {
            // Obter informações da requisição
            $ipAddress = $this->getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // Se userId não foi fornecido, tentar obter da sessão
            if ($userId === null && isset($_SESSION['user_id'])) {
                $userId = $_SESSION['user_id'];
            }
            
            // Preparar dados para log
            $logData = [
                'user_id' => $userId,
                'action' => $action,
                'description' => is_array($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : $data,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'level' => $level
            ];
            
            // Inserir no banco
            $this->db->insert('system_logs', $logData);
            
            // Log em arquivo também (para backup)
            $this->logToFile($level, $action, $data, $userId, $ipAddress);
            
        } catch (Exception $e) {
            // Em caso de erro no log, registrar em arquivo
            error_log("Logger error: " . $e->getMessage());
            $this->logToFile('error', 'Logger database error: ' . $e->getMessage(), $data, $userId);
        }
    }
    
    /**
     * Log específico para transações
     */
    public function logTransaction($action, $transactionId, $data = [], $userId = null) {
        $logData = array_merge($data, ['transaction_id' => $transactionId]);
        $this->log('info', "TRANSACTION: {$action}", $logData, $userId);
    }
    
    /**
     * Log específico para transferências
     */
    public function logTransfer($action, $transferId, $data = [], $userId = null) {
        $logData = array_merge($data, ['transfer_id' => $transferId]);
        $this->log('info', "TRANSFER: {$action}", $logData, $userId);
    }
    
    /**
     * Log específico para taxas administrativas
     */
    public function logAdminFee($action, $feeId, $data = [], $userId = null) {
        $logData = array_merge($data, ['admin_fee_id' => $feeId]);
        $this->log('info', "ADMIN_FEE: {$action}", $logData, $userId);
    }
    
    /**
     * Log específico para API calls
     */
    public function logAPICall($gateway, $endpoint, $method, $response, $userId = null) {
        $this->log('info', "API_CALL: {$gateway}", [
            'endpoint' => $endpoint,
            'method' => $method,
            'response_code' => $response['http_code'] ?? 'unknown',
            'response_size' => strlen($response['body'] ?? ''),
            'execution_time' => $response['execution_time'] ?? 0
        ], $userId);
    }
    
    /**
     * Log de erro crítico
     */
    public function logCritical($message, $data = [], $userId = null) {
        $this->log('critical', $message, $data, $userId);
        
        // Enviar notificação por email para admin (implementar se necessário)
        $this->notifyAdmin($message, $data);
    }
    
    /**
     * Obter logs do sistema com filtros
     */
    public function getLogs($filters = [], $limit = 100, $offset = 0) {
        $where = ['1=1'];
        $params = [];
        
        // Filtro por usuário
        if (!empty($filters['user_id'])) {
            $where[] = 'user_id = :user_id';
            $params['user_id'] = $filters['user_id'];
        }
        
        // Filtro por nível
        if (!empty($filters['level'])) {
            $where[] = 'level = :level';
            $params['level'] = $filters['level'];
        }
        
        // Filtro por ação
        if (!empty($filters['action'])) {
            $where[] = 'action LIKE :action';
            $params['action'] = '%' . $filters['action'] . '%';
        }
        
        // Filtro por data
        if (!empty($filters['date_from'])) {
            $where[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "
            SELECT l.*, u.username, u.full_name 
            FROM system_logs l 
            LEFT JOIN users u ON l.user_id = u.id 
            WHERE {$whereClause} 
            ORDER BY l.created_at DESC 
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Contar logs com filtros
     */
    public function countLogs($filters = []) {
        $where = ['1=1'];
        $params = [];
        
        // Aplicar mesmos filtros do getLogs
        if (!empty($filters['user_id'])) {
            $where[] = 'user_id = :user_id';
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['level'])) {
            $where[] = 'level = :level';
            $params['level'] = $filters['level'];
        }
        
        if (!empty($filters['action'])) {
            $where[] = 'action LIKE :action';
            $params['action'] = '%' . $filters['action'] . '%';
        }
        
        if (!empty($filters['date_from'])) {
            $where[] = 'created_at >= :date_from';
            $params['date_from'] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = 'created_at <= :date_to';
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        $whereClause = implode(' AND ', $where);
        
        return $this->db->count('system_logs', $whereClause, $params);
    }
    
    /**
     * Limpar logs antigos
     */
    public function cleanOldLogs($daysToKeep = 90) {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        $deleted = $this->db->delete('system_logs', 'created_at < :cutoff', ['cutoff' => $cutoffDate]);
        
        $this->log('info', 'Limpeza de logs antigos', [
            'days_to_keep' => $daysToKeep,
            'cutoff_date' => $cutoffDate,
            'deleted_count' => $deleted->rowCount()
        ]);
        
        return $deleted->rowCount();
    }
    
    /**
     * Log em arquivo
     */
    private function logToFile($level, $action, $data, $userId, $ipAddress = null) {
        try {
            $logFile = LOG_FILE_PATH . 'system_' . date('Y-m-d') . '.log';
            
            $logEntry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'level' => strtoupper($level),
                'user_id' => $userId,
                'ip' => $ipAddress ?? $this->getClientIP(),
                'action' => $action,
                'data' => is_array($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : $data
            ];
            
            $logLine = implode(' | ', $logEntry) . PHP_EOL;
            
            file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
            
        } catch (Exception $e) {
            error_log("File logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Obter IP do cliente
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Se for uma lista de IPs, pegar o primeiro
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return 'unknown';
    }
    
    /**
     * Notificar admin sobre erros críticos
     */
    private function notifyAdmin($message, $data) {
        // Implementar notificação por email se necessário
        // Por enquanto, apenas log em arquivo especial
        try {
            $criticalFile = LOG_FILE_PATH . 'critical_' . date('Y-m-d') . '.log';
            $entry = date('Y-m-d H:i:s') . " - CRITICAL: {$message} - Data: " . json_encode($data) . PHP_EOL;
            file_put_contents($criticalFile, $entry, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            error_log("Critical notification error: " . $e->getMessage());
        }
    }
}
?>

<?php
/**
 * Script de Configuração Inicial do Sistema
 * Configura credenciais de exemplo baseadas na documentação da API
 */

require_once 'config/config.php';

$db = Database::getInstance();
$logger = new Logger();

echo "<h1>Configuração Inicial do Sistema</h1>";

try {
    // Verificar se já existe configuração
    $existingConfig = $db->fetch("SELECT id FROM gateway_configs WHERE user_id = 2");
    
    if ($existingConfig) {
        echo "<p style='color: orange;'>⚠️ Configuração já existe para o cliente de exemplo.</p>";
    } else {
        // Credenciais da documentação
        $credentials = [
            'public_key' => 'pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc',
            'secret_key' => 'sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S',
            'withdraw_key' => 'wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m'
        ];
        
        // Inserir configuração para o cliente de exemplo (ID 2)
        $configData = [
            'user_id' => 2,
            'gateway_name' => 'PagDrop',
            'api_base_url' => 'https://api.pagdrop.com',
            'public_key' => $credentials['public_key'],
            'secret_key' => $credentials['secret_key'],
            'withdraw_key' => $credentials['withdraw_key'],
            'admin_fee_percentage' => 10.00,
            'status' => 'active'
        ];
        
        $configId = $db->insert('gateway_configs', $configData);
        
        echo "<p style='color: green;'>✅ Configuração criada com sucesso! ID: {$configId}</p>";
    }
    
    // Configurar chave PIX administrativa de exemplo
    $adminPixKey = $db->fetch("SELECT setting_value FROM system_settings WHERE setting_key = 'admin_pix_key'");
    
    if (empty($adminPixKey['setting_value'])) {
        $pixSettings = [
            ['setting_key' => 'admin_pix_key', 'setting_value' => '<EMAIL>'],
            ['setting_key' => 'admin_pix_key_type', 'setting_value' => 'email']
        ];
        
        foreach ($pixSettings as $setting) {
            $existing = $db->fetch('SELECT id FROM system_settings WHERE setting_key = :key', ['key' => $setting['setting_key']]);
            
            if ($existing) {
                $db->update('system_settings', 
                    ['setting_value' => $setting['setting_value']], 
                    'setting_key = :key', 
                    ['key' => $setting['setting_key']]
                );
            } else {
                $db->insert('system_settings', $setting);
            }
        }
        
        echo "<p style='color: green;'>✅ Chave PIX administrativa configurada!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Chave PIX administrativa já configurada.</p>";
    }
    
    // Testar conexão com a API
    echo "<h2>Teste de Conexão com a API</h2>";
    
    try {
        $gatewayAPI = new GatewayAPI(2); // Cliente de exemplo
        $balance = $gatewayAPI->getBalance();
        
        echo "<p style='color: green;'>✅ Conexão com API bem-sucedida!</p>";
        echo "<p><strong>Saldo disponível:</strong> R$ " . formatMoney(centavosToReais($balance['amount'])) . "</p>";
        echo "<p><strong>Fundos aguardando:</strong> R$ " . formatMoney(centavosToReais($balance['waitingFunds'] ?? 0)) . "</p>";
        echo "<p><strong>Reserva:</strong> R$ " . formatMoney(centavosToReais($balance['reserve'] ?? 0)) . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro na conexão com API: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><em>Isso pode ser normal se as credenciais de exemplo não estiverem ativas.</em></p>";
    }
    
    echo "<h2>Informações de Login</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Administrador:</h3>";
    echo "<p><strong>Usuário:</strong> admin</p>";
    echo "<p><strong>Senha:</strong> password</p>";
    echo "<p><strong>URL:</strong> <a href='login.php'>login.php</a></p>";
    echo "</div>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Cliente de Exemplo:</h3>";
    echo "<p><strong>Usuário:</strong> cliente1</p>";
    echo "<p><strong>Senha:</strong> password</p>";
    echo "<p><strong>URL:</strong> <a href='login.php'>login.php</a></p>";
    echo "</div>";
    
    echo "<h2>Próximos Passos</h2>";
    echo "<ol>";
    echo "<li>Faça login como administrador</li>";
    echo "<li>Vá em <strong>Configurações</strong> e configure sua chave PIX real</li>";
    echo "<li>Crie novos clientes com suas próprias credenciais</li>";
    echo "<li>Configure as taxas administrativas conforme necessário</li>";
    echo "<li>Teste as funcionalidades com o cliente de exemplo</li>";
    echo "</ol>";
    
    echo "<h2>Estrutura de Arquivos Criada</h2>";
    echo "<ul>";
    echo "<li>✅ Banco de dados configurado</li>";
    echo "<li>✅ Classes de integração criadas</li>";
    echo "<li>✅ Painel administrativo funcional</li>";
    echo "<li>✅ Painel do cliente funcional</li>";
    echo "<li>✅ Sistema de taxas automáticas</li>";
    echo "<li>✅ Sistema de logs completo</li>";
    echo "<li>✅ APIs de integração</li>";
    echo "</ul>";
    
    $logger->log('info', 'Sistema configurado com sucesso', [
        'setup_completed' => true,
        'demo_user_configured' => true
    ]);
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro na configuração: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Verifique se o banco de dados foi criado corretamente.</p>";
}

echo "<hr>";
echo "<p><em>Este script pode ser executado novamente sem problemas. Ele não sobrescreverá configurações existentes.</em></p>";
echo "<p><strong>Para segurança, remova este arquivo após a configuração inicial.</strong></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

code {
    background: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
}

.success {
    color: green;
}

.warning {
    color: orange;
}

.error {
    color: red;
}
</style>

Simular Saque Cripto
POST

https://api.pagdrop.com/v1/transfers/simulate

Através dessa rota POST /transfers/simulate você pode simular um saque cripto.

Body Params
amountint32required
Valor bruto do saque em centavos. (Ex: 10000 = R$ 100,00).

coinstringrequired
Moeda desejada para saque. Ex: BTC, ETH ou USDT.

CREDENCIAIS
Basic
sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
:
pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc


<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/transfers/simulate",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => "{\n  \"amount\": 1000,\n  \"coin\": \"BTC\"\n}",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw==",
    "content-type: application/json"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}


RESPONSE REAL DA API
200

{
  "amount": 251,
  "expectedAmount": "0.0000037972768532526473",
  "coin": "BTC",
  "price": "661000",
  "availableNetworks": [
    {
      "network": "btc",
      "networkFee": "0.00000500"
    }
  ]
}


INFORMACAO RESPONSES

200
Objeto de resposta em caso de sucesso na simulação de um saque em cripto

400
Objeto de resposta em caso de falha


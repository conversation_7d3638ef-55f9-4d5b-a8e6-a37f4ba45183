<?php
/**
 * Instalador do Sistema Gateway Universal
 * Execute este arquivo para instalar e configurar o sistema
 */

// Verificar se já foi instalado
if (file_exists('config/installed.lock')) {
    echo "<h1>Sistema já instalado!</h1>";
    echo "<p>O sistema já foi instalado. Se precisar reinstalar, remova o arquivo <code>config/installed.lock</code></p>";
    echo "<p><a href='login.php'>Ir para Login</a></p>";
    exit;
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalação - Gateway Universal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="install-container p-5">
                    <div class="text-center mb-4">
                        <h1 class="h3">🚀 Instalação do Gateway Universal</h1>
                        <p class="text-muted">Sistema Universal de Integração com Gateways de Pagamento</p>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress mb-4">
                        <div class="progress-bar" style="width: <?php echo ($step * 25); ?>%"></div>
                    </div>

                    <?php if ($step == 1): ?>
                    <!-- Passo 1: Verificação de Requisitos -->
                    <h4>📋 Passo 1: Verificação de Requisitos</h4>
                    
                    <?php
                    $requirements = [
                        'PHP 7.4+' => version_compare(PHP_VERSION, '7.4.0', '>='),
                        'PDO Extension' => extension_loaded('pdo'),
                        'PDO MySQL' => extension_loaded('pdo_mysql'),
                        'cURL Extension' => extension_loaded('curl'),
                        'JSON Extension' => extension_loaded('json'),
                        'Session Support' => function_exists('session_start'),
                        'Password Hashing' => function_exists('password_hash')
                    ];
                    
                    $allOk = true;
                    ?>
                    
                    <div class="row">
                        <?php foreach ($requirements as $req => $status): ?>
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span><?php echo $req; ?></span>
                                <span class="<?php echo $status ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo $status ? '✅' : '❌'; ?>
                                </span>
                            </div>
                        </div>
                        <?php 
                        if (!$status) $allOk = false;
                        endforeach; 
                        ?>
                    </div>
                    
                    <?php if ($allOk): ?>
                        <div class="alert alert-success mt-3">
                            ✅ Todos os requisitos foram atendidos!
                        </div>
                        <div class="text-end">
                            <a href="?step=2" class="btn btn-primary">Próximo Passo →</a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger mt-3">
                            ❌ Alguns requisitos não foram atendidos. Verifique sua instalação do PHP.
                        </div>
                    <?php endif; ?>

                    <?php elseif ($step == 2): ?>
                    <!-- Passo 2: Configuração do Banco -->
                    <h4>🗄️ Passo 2: Configuração do Banco de Dados</h4>
                    
                    <?php
                    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                        try {
                            $host = $_POST['db_host'] ?? 'localhost';
                            $user = $_POST['db_user'] ?? 'root';
                            $pass = $_POST['db_pass'] ?? '';
                            $name = $_POST['db_name'] ?? 'gateway_system';
                            
                            // Testar conexão
                            $dsn = "mysql:host={$host};charset=utf8mb4";
                            $pdo = new PDO($dsn, $user, $pass);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            
                            // Criar banco se não existir
                            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$name}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                            $pdo->exec("USE `{$name}`");
                            
                            // Executar SQL
                            $sql = file_get_contents('database.sql');
                            $pdo->exec($sql);
                            
                            // Salvar configurações
                            $configContent = "<?php
// Configurações do Banco de Dados
define('DB_HOST', '{$host}');
define('DB_NAME', '{$name}');
define('DB_USER', '{$user}');
define('DB_PASS', '{$pass}');
define('DB_CHARSET', 'utf8mb4');

// Outras configurações
define('SYSTEM_NAME', 'Gateway Universal');
define('SYSTEM_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/novo/');
define('ADMIN_EMAIL', '<EMAIL>');
define('SESSION_NAME', 'gateway_session');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_HASH_ALGO', PASSWORD_DEFAULT);
define('LOG_LEVEL', 'info');
define('LOG_FILE_PATH', __DIR__ . '/../logs/');
define('API_TIMEOUT', 30);
define('API_USER_AGENT', 'Gateway-Universal/1.0');

date_default_timezone_set('America/Sao_Paulo');
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', LOG_FILE_PATH . 'php_errors.log');

spl_autoload_register(function (\$class) {
    \$paths = [
        __DIR__ . '/../classes/',
        __DIR__ . '/../includes/',
    ];
    
    foreach (\$paths as \$path) {
        \$file = \$path . \$class . '.php';
        if (file_exists(\$file)) {
            require_once \$file;
            return;
        }
    }
});

if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

function generateCSRFToken() {
    if (!isset(\$_SESSION[CSRF_TOKEN_NAME])) {
        \$_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return \$_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken(\$token) {
    return isset(\$_SESSION[CSRF_TOKEN_NAME]) && hash_equals(\$_SESSION[CSRF_TOKEN_NAME], \$token);
}

function sanitizeInput(\$input) {
    if (is_array(\$input)) {
        return array_map('sanitizeInput', \$input);
    }
    return htmlspecialchars(trim(\$input), ENT_QUOTES, 'UTF-8');
}

function isValidEmail(\$email) {
    return filter_var(\$email, FILTER_VALIDATE_EMAIL) !== false;
}

function formatMoney(\$value, \$currency = 'BRL') {
    return number_format(\$value, 2, ',', '.');
}

function centavosToReais(\$centavos) {
    return \$centavos / 100;
}

function reaisToCentavos(\$reais) {
    return \$reais * 100;
}

function generateUniqueId(\$prefix = '') {
    return \$prefix . uniqid() . bin2hex(random_bytes(4));
}

function debugLog(\$message, \$data = null) {
    if (defined('DEBUG') && DEBUG === true) {
        \$log = date('Y-m-d H:i:s') . ' - ' . \$message;
        if (\$data !== null) {
            \$log .= ' - Data: ' . json_encode(\$data);
        }
        error_log(\$log . PHP_EOL, 3, LOG_FILE_PATH . 'debug.log');
    }
}

\$requiredDirs = [
    __DIR__ . '/../logs',
    __DIR__ . '/../uploads',
    __DIR__ . '/../temp'
];

foreach (\$requiredDirs as \$dir) {
    if (!is_dir(\$dir)) {
        mkdir(\$dir, 0755, true);
    }
}

if (file_exists(__DIR__ . '/local_config.php')) {
    require_once __DIR__ . '/local_config.php';
}
?>";
                            
                            file_put_contents('config/config.php', $configContent);
                            
                            $success = "Banco de dados configurado com sucesso!";
                            echo "<script>setTimeout(() => window.location.href = '?step=3', 2000);</script>";
                            
                        } catch (Exception $e) {
                            $error = "Erro: " . $e->getMessage();
                        }
                    }
                    ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                    <?php else: ?>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Host do Banco</label>
                                    <input type="text" class="form-control" name="db_host" value="localhost" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nome do Banco</label>
                                    <input type="text" class="form-control" name="db_name" value="gateway_system" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Usuário</label>
                                    <input type="text" class="form-control" name="db_user" value="root" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Senha</label>
                                    <input type="password" class="form-control" name="db_pass" placeholder="Deixe vazio se não houver senha">
                                </div>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="?step=1" class="btn btn-secondary">← Voltar</a>
                            <button type="submit" class="btn btn-primary">Configurar Banco →</button>
                        </div>
                    </form>
                    
                    <?php endif; ?>

                    <?php elseif ($step == 3): ?>
                    <!-- Passo 3: Teste do Sistema -->
                    <h4>🧪 Passo 3: Teste do Sistema</h4>
                    
                    <?php
                    try {
                        require_once 'config/config.php';
                        
                        $db = Database::getInstance();
                        $auth = new Auth();
                        
                        // Testar login
                        $loginTest = $auth->login('admin', 'password');
                        if ($loginTest) {
                            $auth->logout();
                            $success = "Sistema funcionando perfeitamente!";
                        } else {
                            $error = "Erro no teste de login";
                        }
                        
                    } catch (Exception $e) {
                        $error = "Erro: " . $e->getMessage();
                    }
                    ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                        <div class="text-end">
                            <a href="?step=2" class="btn btn-secondary">← Voltar</a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">✅ Todos os testes passaram!</div>
                        <div class="text-end">
                            <a href="?step=4" class="btn btn-primary">Finalizar →</a>
                        </div>
                    <?php endif; ?>

                    <?php elseif ($step == 4): ?>
                    <!-- Passo 4: Finalização -->
                    <h4>🎉 Instalação Concluída!</h4>
                    
                    <?php
                    // Criar arquivo de lock
                    file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
                    ?>
                    
                    <div class="alert alert-success">
                        <h5>✅ Sistema instalado com sucesso!</h5>
                        <p>O Gateway Universal está pronto para uso.</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6>👨‍💼 Administrador</h6>
                                    <p><strong>Usuário:</strong> admin<br>
                                    <strong>Senha:</strong> password</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6>👤 Cliente Exemplo</h6>
                                    <p><strong>Usuário:</strong> cliente1<br>
                                    <strong>Senha:</strong> password</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <strong>⚠️ Importante:</strong> Altere as senhas padrão antes de usar em produção!
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="login.php" class="btn btn-primary btn-lg">Acessar Sistema</a>
                    </div>

                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

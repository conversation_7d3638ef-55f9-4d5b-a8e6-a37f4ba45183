Formato dos postbacks
Quando uma transação é criada, é possível fornecer uma URL no campo postbackUrl para receber notificações em seu servidor sempre que houver uma atualização na transação. O payload enviado para essa URL seguirá o formato descrito abaixo:

Transação
A entidade Transação representa a movimentação financeira para processar pagamentos na plataforma.

Atributos
paymentMethod: Método de pagamento da transação.
pix: via Pix.
boleto: via boleto bancário.
credit_card: via cartão de crédito.
status: Estado atual da transação.
waiting_payment: Aguardando pagamento.
pending: Em processo de confirmação.
approved: Pagamento aprovado.
refused: Pagamento recusado.
in_protest: Em contestação.
refunded: Pagamento reembolsado.
paid: Pagamento confirmado.
cancelled: Transação cancelada.
chargeback: Estorno realizado.


Exemplo de transação - Pix
O exemplo abaixo representa uma transação com o método de pagamento pix.

JSON

{
  "type": "transaction",
  "url": "https://webhook.exemplo.com",
  "objectId": "123456",
  "data": {
    "id": 123456,
    "tenantId": "abcd1234-5678-90ab-cdef-1234567890ab",
    "companyId": 99,
    "amount": 750,
    "currency": "BRL",
    "paymentMethod": "pix",
    "status": "waiting_payment",
    "installments": 1,
    "paidAt": null,
    "paidAmount": 0,
    "refundedAt": null,
    "refundedAmount": 0,
    "postbackUrl": "https://webhook.exemplo.com",
    "metadata": "{ orderId: 123 }",
    "ip": "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
    "externalRef": "pedido-abc123",
    "secureId": "fake-secure-id-0001",
    "secureUrl": "https://pagamento.exemplo.com/pagar/fake-secure-id-0001",
    "createdAt": "2025-04-29T10:00:00.000Z",
    "updatedAt": "2025-04-29T10:00:00.000Z",
    "payer": null,
    "traceable": false,
    "authorizationCode": null,
    "basePrice": 750,
    "interestRate": 0,
    "items": [
      {
        "title": "Produto Teste Fictício",
        "quantity": 1,
        "tangible": true,
        "unitPrice": 750,
        "externalRef": "item-test-001"
      }
    ],
    "customer": {
      "id": 789,
      "name": "Carlos Exemplar",
      "email": "<EMAIL>",
      "phone": "11987654321",
      "birthdate": "1995-08-20",
      "createdAt": "2025-01-01T12:00:00.000Z",
      "externalRef": null,
      "document": {
        "type": "cpf",
        "number": "00011122233"
      },
      "address": {
        "street": "Rua Fictícia",
        "streetNumber": "100",
        "complement": "Apto 10",
        "zipCode": "12345678",
        "neighborhood": "Bairro Teste",
        "city": "Cidade Exemplo",
        "state": "EX",
        "country": "BR"
      }
    },
    "fee": {
      "netAmount": 738,
      "estimatedFee": 12,
      "fixedAmount": 12,
      "spreadPercent": 1,
      "currency": "BRL"
    },
    "splits": [
      {
        "amount": 750,
        "netAmount": 738,
        "recipientId": 999,
        "chargeProcessingFee": false
      }
    ],
    "refunds": [],
    "pix": {
      "qrcode": "00020101021226870014br.gov.bcb.pix2569pix.pagamento.exemplo.com/pix/v2/abc1234504000053039865802BR5909Carlos EX6008EXEMPLO62070503***6304FAKE",
      "end2EndId": null,
      "receiptUrl": null,
      "expirationDate": "2025-04-30"
    },
    "boleto": null,
    "card": null,
    "refusedReason": null,
    "shipping": null,
    "delivery": null,
    "threeDS": {
      "redirectUrl": "https://minhaapi.com/redirect",
      "returnUrl": "https://minhaapi.com/return",
      "token": "jkhasdJHKHJKUASJKHhjksadhjkjkhHJjsfhd43ASasdfuih23jkKHBASVLdasfkjlh43"
    }
  }
}


Transferência
A entidade Transferência representa o envio de fundos entre contas, especialmente para transações via Pix.

Atributos
status: Estado atual da transferência.
COMPLETED: concluída com sucesso.
PROCESSING: em processo de execução.
CANCELLED: cancelada.
REFUSED: recusada.
PENDING_ANALYSIS: em análise.
PENDING_QUEUE: na fila de processamento.
pixKeyType: Tipo de chave Pix utilizada na transferência.
cpf: Chave Pix vinculada ao CPF do destinatário.
cnpj: Chave Pix vinculada ao CNPJ do destinatário.
email: Chave Pix vinculada a um e-mail.
phone: Chave Pix vinculada a um número de telefone.
evp: Chave aleatória (Endereço Virtual de Pagamento - EVP).
copypaste: Chave copia e cola.

Esses atributos permitem o acompanhamento detalhado do status da transferência e o tipo de chave Pix utilizada.

Exemplo de transação - Transferência
JSON

{
  "id": 999,
  "type": "transfer",
  "objectId": "999",
  "url": "https://webhook.exemplo.com/retorno",
  "data": {
    "id": 999,
    "tenantId": "abcd1234-ef56-7890-abcd-1234567890ef",
    "companyId": 77,
    "amount": 500,
    "netAmount": 450,
    "fee": 50,
    "currency": "BRL",
    "method": "fiat",
    "status": "COMPLETED",
    "externalRef": null,
    "isExternal": true,
    "pixKey": "00011122233",
    "pixKeyType": "cpf",
    "pixEnd2EndId": null,
    "cryptoWallet": null,
    "cryptoNetwork": null,
    "cryptoAddress": null,
    "description": "Pagamento efetuado",
    "metadata": "{"notaFiscal":"NF-0001"}",
    "postbackUrl": "https://webhook.exemplo.com/retorno",
    "history": [],
    "transferredAt": "2025-04-28T15:30:45.000Z",
    "processedAt": "2025-04-28T15:30:30.000Z",
    "canceledAt": null,
    "createdAt": "2025-04-28T15:30:00.000Z",
    "updatedAt": "2025-04-28T15:30:45.001Z"
  }
}




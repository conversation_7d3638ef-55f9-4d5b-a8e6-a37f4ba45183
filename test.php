<?php
/**
 * Teste básico do PHP
 */

// Ativar exibição de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnóstico do Sistema</h1>";

echo "<h2>1. Informações do PHP</h2>";
echo "<p><strong>Versão do PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Data/Hora:</strong> " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>2. Teste de Arquivos</h2>";

$files = [
    'config/config.php',
    'classes/Database.php',
    'classes/Auth.php',
    'classes/Logger.php',
    'database.sql'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ {$file} - Existe</p>";
    } else {
        echo "<p style='color: red;'>❌ {$file} - Não encontrado</p>";
    }
}

echo "<h2>3. Teste de Configuração</h2>";

try {
    if (file_exists('config/config.php')) {
        echo "<p>Tentando incluir config.php...</p>";
        require_once 'config/config.php';
        echo "<p style='color: green;'>✅ Config carregado com sucesso</p>";
        
        echo "<p><strong>DB_HOST:</strong> " . (defined('DB_HOST') ? DB_HOST : 'Não definido') . "</p>";
        echo "<p><strong>DB_NAME:</strong> " . (defined('DB_NAME') ? DB_NAME : 'Não definido') . "</p>";
        echo "<p><strong>SYSTEM_NAME:</strong> " . (defined('SYSTEM_NAME') ? SYSTEM_NAME : 'Não definido') . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Arquivo config/config.php não encontrado</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro ao carregar config: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>4. Teste de Classes</h2>";

try {
    if (class_exists('Database')) {
        echo "<p style='color: green;'>✅ Classe Database carregada</p>";
        
        $db = Database::getInstance();
        echo "<p style='color: green;'>✅ Instância do Database criada</p>";
        
        $test = $db->fetch("SELECT 1 as test");
        echo "<p style='color: green;'>✅ Consulta de teste funcionou</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Classe Database não encontrada</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro no Database: " . htmlspecialchars($e->getMessage()) . "</p>";
}

try {
    if (class_exists('Auth')) {
        echo "<p style='color: green;'>✅ Classe Auth carregada</p>";
        
        $auth = new Auth();
        echo "<p style='color: green;'>✅ Instância do Auth criada</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Classe Auth não encontrada</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro no Auth: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>5. Teste de Sessão</h2>";

try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p style='color: green;'>✅ Sessão iniciada</p>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro na sessão: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>6. Logs de Erro</h2>";

$errorLog = 'logs/php_errors.log';
if (file_exists($errorLog)) {
    $errors = file_get_contents($errorLog);
    if (!empty($errors)) {
        echo "<pre style='background: #f8f8f8; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($errors);
        echo "</pre>";
    } else {
        echo "<p>Arquivo de log vazio</p>";
    }
} else {
    echo "<p>Arquivo de log não encontrado</p>";
}

echo "<hr>";
echo "<p><strong>Se todos os testes passaram, tente acessar:</strong></p>";
echo "<p><a href='login.php'>login.php</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

pre {
    font-size: 12px;
}
</style>

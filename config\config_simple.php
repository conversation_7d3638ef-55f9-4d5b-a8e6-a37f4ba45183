<?php
/**
 * Configuração Simplificada - Sistema Gateway
 */

// Configurações do Banco de Dados
define('DB_HOST', 'localhost');
define('DB_NAME', 'gateway_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Configurações Básicas
define('SYSTEM_NAME', 'Gateway Universal');
define('SYSTEM_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/novo/');
define('ADMIN_EMAIL', '<EMAIL>');
define('SESSION_NAME', 'gateway_session');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_HASH_ALGO', PASSWORD_DEFAULT);
define('LOG_LEVEL', 'info');
define('LOG_FILE_PATH', __DIR__ . '/../logs/');
define('API_TIMEOUT', 30);
define('API_USER_AGENT', 'Gateway-Universal/1.0');

// Configurar timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurar erros
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Criar diretórios necessários
$dirs = [
    __DIR__ . '/../logs',
    __DIR__ . '/../uploads',
    __DIR__ . '/../temp'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
}

// Autoload simples
function loadClass($class) {
    $paths = [
        __DIR__ . '/../classes/' . $class . '.php',
        __DIR__ . '/../includes/' . $class . '.php'
    ];
    
    foreach ($paths as $file) {
        if (file_exists($file)) {
            require_once $file;
            return true;
        }
    }
    return false;
}

spl_autoload_register('loadClass');

// Iniciar sessão
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Funções básicas
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function formatMoney($value) {
    return number_format($value, 2, ',', '.');
}

function centavosToReais($centavos) {
    return $centavos / 100;
}

function reaisToCentavos($reais) {
    return $reais * 100;
}

function generateUniqueId($prefix = '') {
    return $prefix . uniqid() . bin2hex(random_bytes(4));
}
?>

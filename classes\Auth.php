<?php
/**
 * Classe de Autenticação
 * Sistema Universal de Gateways
 */

class Auth {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logger = new Logger();
    }
    
    /**
     * Fazer login do usuário
     */
    public function login($username, $password, $remember = false) {
        try {
            // Buscar usuário
            $user = $this->db->fetch(
                "SELECT * FROM users WHERE (username = :username OR email = :username) AND status = 'active'",
                ['username' => $username]
            );
            
            if (!$user || !password_verify($password, $user['password'])) {
                $this->logger->log('warning', 'Tentativa de login inválida', [
                    'username' => $username,
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                return false;
            }
            
            // Criar sessão
            $this->createSession($user);
            
            // Log de sucesso
            $this->logger->log('info', 'Login realizado com sucesso', [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'user_type' => $user['user_type']
            ], $user['id']);
            
            // Atualizar último login
            $this->db->update('users', 
                ['updated_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $user['id']]
            );
            
            return true;
            
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro no login: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Criar sessão do usuário
     */
    private function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();
        
        // Regenerar ID da sessão por segurança
        session_regenerate_id(true);
    }
    
    /**
     * Fazer logout
     */
    public function logout() {
        if ($this->isLoggedIn()) {
            $this->logger->log('info', 'Logout realizado', [], $_SESSION['user_id']);
        }
        
        // Limpar sessão
        $_SESSION = [];
        session_destroy();
        
        // Iniciar nova sessão
        session_start();
        
        return true;
    }
    
    /**
     * Verificar se usuário está logado
     */
    public function isLoggedIn() {
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }
    
    /**
     * Verificar se usuário é admin
     */
    public function isAdmin() {
        return $this->isLoggedIn() && $_SESSION['user_type'] === 'admin';
    }
    
    /**
     * Verificar se usuário é cliente
     */
    public function isClient() {
        return $this->isLoggedIn() && $_SESSION['user_type'] === 'client';
    }
    
    /**
     * Obter dados do usuário logado
     */
    public function getUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'email' => $_SESSION['email'],
            'full_name' => $_SESSION['full_name'],
            'user_type' => $_SESSION['user_type']
        ];
    }
    
    /**
     * Obter ID do usuário logado
     */
    public function getUserId() {
        return $this->isLoggedIn() ? $_SESSION['user_id'] : null;
    }
    
    /**
     * Verificar permissão de acesso
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Admin tem todas as permissões
        if ($this->isAdmin()) {
            return true;
        }
        
        // Definir permissões por tipo de usuário
        $clientPermissions = [
            'view_own_data',
            'view_own_transactions',
            'view_own_transfers',
            'create_transfer',
            'view_own_balance'
        ];
        
        if ($this->isClient() && in_array($permission, $clientPermissions)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Verificar se pode acessar dados de um usuário específico
     */
    public function canAccessUser($userId) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Admin pode acessar qualquer usuário
        if ($this->isAdmin()) {
            return true;
        }
        
        // Cliente só pode acessar seus próprios dados
        return $this->getUserId() == $userId;
    }
    
    /**
     * Registrar novo usuário (apenas admin pode criar)
     */
    public function register($data) {
        if (!$this->isAdmin()) {
            throw new Exception('Apenas administradores podem criar usuários');
        }
        
        try {
            // Validar dados
            $this->validateUserData($data);
            
            // Verificar se username/email já existem
            if ($this->userExists($data['username'], $data['email'])) {
                throw new Exception('Username ou email já existem');
            }
            
            // Hash da senha
            $data['password'] = password_hash($data['password'], PASSWORD_HASH_ALGO);
            
            // Inserir usuário
            $userId = $this->db->insert('users', [
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => $data['password'],
                'full_name' => $data['full_name'],
                'user_type' => $data['user_type'] ?? 'client',
                'status' => 'active'
            ]);
            
            $this->logger->log('info', 'Usuário criado com sucesso', [
                'new_user_id' => $userId,
                'username' => $data['username'],
                'user_type' => $data['user_type'] ?? 'client'
            ], $this->getUserId());
            
            return $userId;
            
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao criar usuário: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Validar dados do usuário
     */
    private function validateUserData($data) {
        $required = ['username', 'email', 'password', 'full_name'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception("Campo {$field} é obrigatório");
            }
        }
        
        if (!isValidEmail($data['email'])) {
            throw new Exception('Email inválido');
        }
        
        if (strlen($data['password']) < 6) {
            throw new Exception('Senha deve ter pelo menos 6 caracteres');
        }
        
        if (strlen($data['username']) < 3) {
            throw new Exception('Username deve ter pelo menos 3 caracteres');
        }
    }
    
    /**
     * Verificar se usuário já existe
     */
    private function userExists($username, $email) {
        return $this->db->exists('users', 
            'username = :username OR email = :email', 
            ['username' => $username, 'email' => $email]
        );
    }
    
    /**
     * Alterar senha
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // Verificar se pode alterar senha deste usuário
            if (!$this->canAccessUser($userId)) {
                throw new Exception('Sem permissão para alterar senha deste usuário');
            }
            
            // Buscar usuário atual
            $user = $this->db->fetch('SELECT password FROM users WHERE id = :id', ['id' => $userId]);
            
            if (!$user) {
                throw new Exception('Usuário não encontrado');
            }
            
            // Verificar senha atual (admin pode pular esta verificação)
            if (!$this->isAdmin() && !password_verify($currentPassword, $user['password'])) {
                throw new Exception('Senha atual incorreta');
            }
            
            // Validar nova senha
            if (strlen($newPassword) < 6) {
                throw new Exception('Nova senha deve ter pelo menos 6 caracteres');
            }
            
            // Atualizar senha
            $hashedPassword = password_hash($newPassword, PASSWORD_HASH_ALGO);
            $this->db->update('users', 
                ['password' => $hashedPassword], 
                'id = :id', 
                ['id' => $userId]
            );
            
            $this->logger->log('info', 'Senha alterada com sucesso', ['target_user_id' => $userId], $this->getUserId());
            
            return true;
            
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao alterar senha: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Middleware para verificar autenticação
     */
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: ' . BASE_URL . 'login.php');
            exit;
        }
    }
    
    /**
     * Middleware para verificar se é admin
     */
    public function requireAdmin() {
        $this->requireLogin();
        if (!$this->isAdmin()) {
            header('Location: ' . BASE_URL . 'dashboard.php');
            exit;
        }
    }
}
?>

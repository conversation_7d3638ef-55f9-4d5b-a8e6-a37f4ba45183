<?php
if (!isset($auth) || !$auth->isLoggedIn()) {
    header('Location: ' . BASE_URL . 'login.php');
    exit;
}

$user = $auth->getUser();
?>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?php echo BASE_URL; ?>dashboard.php">
            <i class="fas fa-credit-card me-2"></i>
            <?php echo SYSTEM_NAME; ?>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <?php if ($auth->isAdmin()): ?>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>dashboard.php">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>admin/users.php">
                        <i class="fas fa-users me-1"></i>Clientes
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>admin/transactions.php">
                        <i class="fas fa-credit-card me-1"></i>Transações
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>admin/transfers.php">
                        <i class="fas fa-exchange-alt me-1"></i>Transferências
                    </a>
                </li>
                <?php endif; ?>
            </ul>
            
            <ul class="navbar-nav">
                <!-- Notificações -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="badge bg-danger badge-counter">3+</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in">
                        <h6 class="dropdown-header">
                            <i class="fas fa-bell me-2"></i>Notificações
                        </h6>
                        <a class="dropdown-item d-flex align-items-center" href="#">
                            <div class="mr-3">
                                <div class="icon-circle bg-primary">
                                    <i class="fas fa-file-alt text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-gray-500">12 de Dezembro, 2023</div>
                                <span class="font-weight-bold">Nova transação recebida!</span>
                            </div>
                        </a>
                        <a class="dropdown-item d-flex align-items-center" href="#">
                            <div class="mr-3">
                                <div class="icon-circle bg-success">
                                    <i class="fas fa-donate text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-gray-500">7 de Dezembro, 2023</div>
                                Taxa administrativa enviada com sucesso
                            </div>
                        </a>
                        <a class="dropdown-item d-flex align-items-center" href="#">
                            <div class="mr-3">
                                <div class="icon-circle bg-warning">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-gray-500">2 de Dezembro, 2023</div>
                                Falha no envio de taxa - verificar configuração PIX
                            </div>
                        </a>
                        <a class="dropdown-item text-center small text-gray-500" href="<?php echo BASE_URL; ?>admin/notifications.php">
                            Ver Todas as Notificações
                        </a>
                    </div>
                </li>
                
                <!-- Perfil do Usuário -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span class="d-none d-lg-inline text-gray-600 small">
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in">
                        <h6 class="dropdown-header">
                            <i class="fas fa-user me-2"></i>
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>profile.php">
                            <i class="fas fa-user fa-sm fa-fw me-2 text-gray-400"></i>
                            Perfil
                        </a>
                        <?php if ($auth->isAdmin()): ?>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/settings.php">
                            <i class="fas fa-cogs fa-sm fa-fw me-2 text-gray-400"></i>
                            Configurações
                        </a>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/logs.php">
                            <i class="fas fa-list fa-sm fa-fw me-2 text-gray-400"></i>
                            Logs de Atividade
                        </a>
                        <?php endif; ?>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#logoutModal">
                            <i class="fas fa-sign-out-alt fa-sm fa-fw me-2 text-gray-400"></i>
                            Sair
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Modal de Logout -->
<div class="modal fade" id="logoutModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Logout</h5>
                <button class="btn-close" type="button" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Tem certeza que deseja sair do sistema?
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancelar</button>
                <a class="btn btn-primary" href="<?php echo BASE_URL; ?>logout.php">Sair</a>
            </div>
        </div>
    </div>
</div>

<style>
body {
    padding-top: 56px;
}

.navbar-brand {
    font-weight: bold;
}

.badge-counter {
    position: absolute;
    transform: scale(0.7);
    transform-origin: top right;
    right: 0.25rem;
    top: -0.25rem;
}

.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdown-menu {
    min-width: 15rem;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
}

.dropdown-header {
    padding: 0.5rem 1.5rem;
    margin-bottom: 0;
    font-size: 0.85rem;
    color: #858796;
    font-weight: 800;
}

.animated--grow-in {
    animation-name: growIn;
    animation-duration: 200ms;
    animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1);
}

@keyframes growIn {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
</style>

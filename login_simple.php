<?php
/**
 * Login Simplificado para Teste
 */

// Ativar exibição de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔐 Login Simplificado</h1>";

try {
    // Tentar carregar configuração
    if (file_exists('config/config_simple.php')) {
        require_once 'config/config_simple.php';
        echo "<p style='color: green;'>✅ Configuração carregada</p>";
    } else {
        throw new Exception("Arquivo de configuração não encontrado");
    }
    
    // Testar conexão com banco
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Conexão com banco estabelecida</p>";
    
    $error = '';
    $success = '';
    
    // Processar login
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            $error = 'Usuário e senha são obrigatórios';
        } else {
            try {
                // Buscar usuário
                $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active'");
                $stmt->execute([$username, $username]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user && password_verify($password, $user['password'])) {
                    // Login bem-sucedido
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['user_type'] = $user['user_type'];
                    $_SESSION['logged_in'] = true;
                    
                    $success = 'Login realizado com sucesso!';
                    
                    // Redirecionar após 2 segundos
                    if ($user['user_type'] === 'admin') {
                        echo "<script>setTimeout(() => window.location.href = 'dashboard.php', 2000);</script>";
                    } else {
                        echo "<script>setTimeout(() => window.location.href = 'client/dashboard.php', 2000);</script>";
                    }
                } else {
                    $error = 'Usuário ou senha inválidos';
                }
            } catch (Exception $e) {
                $error = 'Erro no login: ' . $e->getMessage();
            }
        }
    }
    
    // Verificar se já está logado
    if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ Usuário já está logado!</h3>";
        echo "<p><strong>Usuário:</strong> " . htmlspecialchars($_SESSION['username']) . "</p>";
        echo "<p><strong>Tipo:</strong> " . htmlspecialchars($_SESSION['user_type']) . "</p>";
        echo "<p><a href='logout_simple.php'>Fazer Logout</a></p>";
        echo "</div>";
    }
    
    ?>
    
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Login - Gateway Universal</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 400px;
                margin: 50px auto;
                padding: 20px;
                background: #f5f5f5;
            }
            .login-box {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .form-group {
                margin-bottom: 15px;
            }
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            input[type="text"], input[type="password"] {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                box-sizing: border-box;
            }
            button {
                width: 100%;
                padding: 12px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                cursor: pointer;
            }
            button:hover {
                background: #0056b3;
            }
            .alert {
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
            }
            .alert-danger {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .alert-success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
        </style>
    </head>
    <body>
        <div class="login-box">
            <h2 style="text-align: center; margin-bottom: 30px;">🔐 Login</h2>
            
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="username">Usuário:</label>
                    <input type="text" id="username" name="username" required 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="password">Senha:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit">Entrar</button>
            </form>
            
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h4>🔑 Credenciais de Teste:</h4>
                <p><strong>Admin:</strong> admin / password</p>
                <p><strong>Cliente:</strong> cliente1 / password</p>
            </div>
            
            <div style="margin-top: 15px; text-align: center;">
                <a href="test.php">🔍 Diagnóstico do Sistema</a> |
                <a href="check_database.php">🗄️ Verificar Banco</a>
            </div>
        </div>
    </body>
    </html>
    
    <?php
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Erro Fatal</h3>";
    echo "<p><strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<h3>🔧 Soluções:</h3>";
    echo "<ol>";
    echo "<li><a href='check_database.php'>Verificar e configurar banco de dados</a></li>";
    echo "<li><a href='test.php'>Executar diagnóstico completo</a></li>";
    echo "<li>Verificar se o XAMPP está rodando</li>";
    echo "<li>Verificar se o MySQL está ativo</li>";
    echo "</ol>";
}
?>

Atualizar Status de Entrega
PUT
https://api.pagdrop.com/v1/transactions/{id}/delivery

Através dessa rota PUT transactions/{id}/delivery você pode atualizar o status de entrega de uma venda.

Path Params
idint32required
ID da transação que terá o status de entrega atualizado.

Body Params
statusstringrequired
Novo status de entrega. Valores possíveis: waiting, in_transit, delivered.

trackingCodestringrequired
Código de rastreamento da encomenda.


CREDENCIAIS
Basic
sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
:
pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc

GERAR O 

"authorization: Basic"


<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/transactions/152545/delivery",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "PUT",
  CURLOPT_POSTFIELDS => "{\n  \"status\": \"waiting\",\n  \"trackingCode\": \"135255\"\n}",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw==",
    "content-type: application/json"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}

RESPONSE 400 REAL DA API

{
  "message": "Transação não encontrada."
}



RESPONSES

200
Objeto de resposta em caso de sucesso

400
Objeto de resposta em caso de falha

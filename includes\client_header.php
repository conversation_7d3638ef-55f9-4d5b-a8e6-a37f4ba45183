<?php
if (!isset($auth) || !$auth->isLoggedIn()) {
    header('Location: ' . BASE_URL . 'login.php');
    exit;
}

$user = $auth->getUser();
?>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?php echo BASE_URL; ?>client/dashboard.php">
            <i class="fas fa-credit-card me-2"></i>
            <?php echo SYSTEM_NAME; ?>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>client/dashboard.php">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>client/transactions.php">
                        <i class="fas fa-credit-card me-1"></i>Transações
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>client/transfers.php">
                        <i class="fas fa-exchange-alt me-1"></i>Saques
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>client/balance.php">
                        <i class="fas fa-wallet me-1"></i>Saldo
                    </a>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <!-- Saldo Rápido -->
                <li class="nav-item">
                    <span class="navbar-text me-3">
                        <i class="fas fa-wallet me-1"></i>
                        <span id="quick-balance">Carregando...</span>
                    </span>
                </li>
                
                <!-- Perfil do Usuário -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span class="d-none d-lg-inline text-white small">
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in">
                        <h6 class="dropdown-header">
                            <i class="fas fa-user me-2"></i>
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>client/profile.php">
                            <i class="fas fa-user fa-sm fa-fw me-2 text-gray-400"></i>
                            Meu Perfil
                        </a>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>client/settings.php">
                            <i class="fas fa-cogs fa-sm fa-fw me-2 text-gray-400"></i>
                            Configurações
                        </a>
                        <a class="dropdown-item" href="<?php echo BASE_URL; ?>client/help.php">
                            <i class="fas fa-question-circle fa-sm fa-fw me-2 text-gray-400"></i>
                            Ajuda
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#logoutModal">
                            <i class="fas fa-sign-out-alt fa-sm fa-fw me-2 text-gray-400"></i>
                            Sair
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Modal de Logout -->
<div class="modal fade" id="logoutModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Logout</h5>
                <button class="btn-close" type="button" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Tem certeza que deseja sair do sistema?
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancelar</button>
                <a class="btn btn-primary" href="<?php echo BASE_URL; ?>logout.php">Sair</a>
            </div>
        </div>
    </div>
</div>

<style>
body {
    padding-top: 56px;
}

.navbar-brand {
    font-weight: bold;
}

.navbar-text {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.9rem;
}

.dropdown-menu {
    min-width: 15rem;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
}

.dropdown-header {
    padding: 0.5rem 1.5rem;
    margin-bottom: 0;
    font-size: 0.85rem;
    color: #858796;
    font-weight: 800;
}

.animated--grow-in {
    animation-name: growIn;
    animation-duration: 200ms;
    animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1);
}

@keyframes growIn {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

#quick-balance {
    font-weight: 600;
    color: #fff;
}
</style>

<script>
// Carregar saldo rápido
document.addEventListener('DOMContentLoaded', function() {
    loadQuickBalance();
    
    // Atualizar saldo a cada 30 segundos
    setInterval(loadQuickBalance, 30000);
});

function loadQuickBalance() {
    fetch('<?php echo BASE_URL; ?>api/quick_balance.php')
        .then(response => response.json())
        .then(data => {
            const balanceElement = document.getElementById('quick-balance');
            if (data.success) {
                balanceElement.textContent = 'R$ ' + data.balance;
                balanceElement.style.color = '#fff';
            } else {
                balanceElement.textContent = 'Erro';
                balanceElement.style.color = '#ffc107';
            }
        })
        .catch(error => {
            document.getElementById('quick-balance').textContent = 'Offline';
            document.getElementById('quick-balance').style.color = '#dc3545';
        });
}
</script>

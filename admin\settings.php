<?php
require_once '../config/config.php';

$auth = new Auth();
$auth->requireAdmin();

$db = Database::getInstance();
$logger = new Logger();

$message = '';
$error = '';

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Token de segurança inválido';
    } else {
        try {
            $settings = [
                'admin_pix_key' => sanitizeInput($_POST['admin_pix_key']),
                'admin_pix_key_type' => $_POST['admin_pix_key_type'],
                'default_gateway_name' => sanitizeInput($_POST['default_gateway_name']),
                'default_api_base_url' => sanitizeInput($_POST['default_api_base_url']),
                'system_name' => sanitizeInput($_POST['system_name'])
            ];
            
            foreach ($settings as $key => $value) {
                $existing = $db->fetch('SELECT id FROM system_settings WHERE setting_key = :key', ['key' => $key]);
                
                if ($existing) {
                    $db->update('system_settings', 
                        ['setting_value' => $value], 
                        'setting_key = :key', 
                        ['key' => $key]
                    );
                } else {
                    $db->insert('system_settings', [
                        'setting_key' => $key,
                        'setting_value' => $value
                    ]);
                }
            }
            
            $logger->log('info', 'Configurações do sistema atualizadas', $settings, $auth->getUserId());
            $message = 'Configurações salvas com sucesso!';
            
        } catch (Exception $e) {
            $error = 'Erro ao salvar configurações: ' . $e->getMessage();
        }
    }
}

// Obter configurações atuais
$currentSettings = [];
$settingsFromDb = $db->fetchAll('SELECT setting_key, setting_value FROM system_settings');
foreach ($settingsFromDb as $setting) {
    $currentSettings[$setting['setting_key']] = $setting['setting_value'];
}

$pageTitle = 'Configurações do Sistema';
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/admin_header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/admin_sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-cog me-2"></i>Configurações do Sistema
                    </h1>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <form method="POST" data-validate>
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <!-- Configurações Gerais -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-cogs me-2"></i>Configurações Gerais
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="system_name" class="form-label">Nome do Sistema</label>
                                        <input type="text" class="form-control" id="system_name" name="system_name" 
                                               value="<?php echo htmlspecialchars($currentSettings['system_name'] ?? SYSTEM_NAME); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="default_gateway_name" class="form-label">Nome Padrão do Gateway</label>
                                        <input type="text" class="form-control" id="default_gateway_name" name="default_gateway_name" 
                                               value="<?php echo htmlspecialchars($currentSettings['default_gateway_name'] ?? 'PagDrop'); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="default_api_base_url" class="form-label">URL Base Padrão da API</label>
                                <input type="url" class="form-control" id="default_api_base_url" name="default_api_base_url" 
                                       value="<?php echo htmlspecialchars($currentSettings['default_api_base_url'] ?? 'https://api.pagdrop.com'); ?>" required>
                                <div class="form-text">URL base que será usada como padrão para novos clientes</div>
                            </div>
                        </div>
                    </div>

                    <!-- Configurações de Taxa Administrativa -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-percentage me-2"></i>Taxa Administrativa
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Importante:</strong> Configure a chave PIX para onde serão enviadas as taxas administrativas automaticamente.
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="admin_pix_key" class="form-label">Chave PIX para Taxas</label>
                                        <input type="text" class="form-control" id="admin_pix_key" name="admin_pix_key" 
                                               value="<?php echo htmlspecialchars($currentSettings['admin_pix_key'] ?? ''); ?>" required>
                                        <div class="form-text">Chave PIX onde serão depositadas as taxas administrativas</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="admin_pix_key_type" class="form-label">Tipo da Chave PIX</label>
                                        <select class="form-select" id="admin_pix_key_type" name="admin_pix_key_type" required>
                                            <option value="email" <?php echo ($currentSettings['admin_pix_key_type'] ?? 'email') === 'email' ? 'selected' : ''; ?>>Email</option>
                                            <option value="cpf" <?php echo ($currentSettings['admin_pix_key_type'] ?? '') === 'cpf' ? 'selected' : ''; ?>>CPF</option>
                                            <option value="cnpj" <?php echo ($currentSettings['admin_pix_key_type'] ?? '') === 'cnpj' ? 'selected' : ''; ?>>CNPJ</option>
                                            <option value="phone" <?php echo ($currentSettings['admin_pix_key_type'] ?? '') === 'phone' ? 'selected' : ''; ?>>Telefone</option>
                                            <option value="evp" <?php echo ($currentSettings['admin_pix_key_type'] ?? '') === 'evp' ? 'selected' : ''; ?>>Chave Aleatória</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informações do Sistema -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>Informações do Sistema
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th>Versão do Sistema:</th>
                                            <td><?php echo SYSTEM_VERSION; ?></td>
                                        </tr>
                                        <tr>
                                            <th>Versão do PHP:</th>
                                            <td><?php echo PHP_VERSION; ?></td>
                                        </tr>
                                        <tr>
                                            <th>Servidor Web:</th>
                                            <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Desconhecido'; ?></td>
                                        </tr>
                                        <tr>
                                            <th>Banco de Dados:</th>
                                            <td>MySQL <?php echo $db->fetch('SELECT VERSION() as version')['version']; ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th>Timezone:</th>
                                            <td><?php echo date_default_timezone_get(); ?></td>
                                        </tr>
                                        <tr>
                                            <th>Data/Hora Atual:</th>
                                            <td><?php echo date('d/m/Y H:i:s'); ?></td>
                                        </tr>
                                        <tr>
                                            <th>Uso de Memória:</th>
                                            <td><?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</td>
                                        </tr>
                                        <tr>
                                            <th>Limite de Memória:</th>
                                            <td><?php echo ini_get('memory_limit'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Estatísticas do Sistema -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-bar me-2"></i>Estatísticas do Sistema
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-primary"><?php echo $db->count('users', "user_type = 'client'"); ?></h4>
                                        <small class="text-muted">Total de Clientes</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-success"><?php echo $db->count('transactions'); ?></h4>
                                        <small class="text-muted">Total de Transações</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-info"><?php echo $db->count('transfers'); ?></h4>
                                        <small class="text-muted">Total de Transferências</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-warning"><?php echo $db->count('system_logs'); ?></h4>
                                        <small class="text-muted">Registros de Log</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Ações de Manutenção -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-tools me-2"></i>Ações de Manutenção
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="logs.php" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-list-alt me-2"></i>Ver Logs
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="backup.php" class="btn btn-warning w-100 mb-2">
                                        <i class="fas fa-database me-2"></i>Backup
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="test_api.php" class="btn btn-success w-100 mb-2">
                                        <i class="fas fa-vial me-2"></i>Testar API
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-secondary w-100 mb-2" onclick="clearCache()">
                                        <i class="fas fa-broom me-2"></i>Limpar Cache
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salvar Configurações
                        </button>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        function clearCache() {
            if (confirm('Tem certeza que deseja limpar o cache do sistema?')) {
                fetch('../api/clear_cache.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        AdminUtils.showToast('Cache limpo com sucesso!', 'success');
                    } else {
                        AdminUtils.showToast('Erro ao limpar cache', 'error');
                    }
                })
                .catch(error => {
                    AdminUtils.showToast('Erro na operação', 'error');
                });
            }
        }
    </script>
</body>
</html>

Criar Venda
POST
https://api.pagdrop.com/v1/transactions

Para processar uma transação, utilize a rota /transactions. Essa rota é compatível com pagamentos via cartão de crédito, boleto bancário e PIX.

amountint32required
Valor total da transação em centavos. (Ex: 100 = R$ 1,00).

paymentMethodstringrequired
Meio de pagamento. Valores possíveis: credit_card, boleto, pix.

itemsarray of objectsrequired
Lista dos produtos vendidos. Ao menos um item é obrigatório.

items OBJECT 0
titlestringrequired
Nome do produto.

unitPriceint32required
Preço unitário em centavos. (Ex: 100 = R$1,00).

quantityint32required
Quantidade do produto na transação.

tangiblebooleanrequired
Se o produto é físico ou não.
false ou true

customerobjectrequired
Informações sobre o cliente.

customer OBJECT
namestringrequired
Nome do cliente.

emailstringrequired
E-mail do cliente. Caso informe um e-mail já existente, os dados do cliente correspondente serão reaproveitados.

documentobjectrequired
Documento do cliente.

document OBJECT
numberstringrequired
Número do documento.

typestringrequired
Tipo do documento. Valores possíveis: cpf, cnpj


CREDENCIAIS
Basic
sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
:
pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc

GERAR O  

"authorization: Basic"


<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/transactions",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "POST",
  CURLOPT_POSTFIELDS => "{\n  \"amount\": 1000,\n  \"paymentMethod\": \"pix\",\n  \"items\": [\n    {\n      \"title\": \"Pagamento Venda 01\",\n      \"unitPrice\": 1000,\n      \"quantity\": 1,\n      \"tangible\": false\n    }\n  ],\n  \"customer\": {\n    \"name\": \"Gustavo Dias Pinto\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"11925889655\",\n    \"document\": {\n      \"number\": \"42804615898\",\n      \"type\": \"cpf\"\n    }\n  }\n}",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw==",
    "content-type: application/json"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}

RESPONSE 200 REAL DA API

{
  "id": 12451803,
  "tenantId": "7a45cd92-63f7-4e31-92ed-5ef13b9333fa",
  "companyId": 17696,
  "amount": 1000,
  "currency": "BRL",
  "paymentMethod": "pix",
  "status": "waiting_payment",
  "installments": 1,
  "paidAt": null,
  "paidAmount": 0,
  "refundedAt": null,
  "refundedAmount": 0,
  "redirectUrl": null,
  "returnUrl": null,
  "postbackUrl": null,
  "metadata": null,
  "ip": null,
  "externalRef": null,
  "secureId": "864e6cea-2974-462f-a77d-455eae7da7fd",
  "secureUrl": "864e6cea-2974-462f-a77d-455eae7da7fd",
  "createdAt": "2025-07-31T02:01:52.345Z",
  "updatedAt": "2025-07-31T02:01:52.345Z",
  "payer": null,
  "traceable": false,
  "authorizationCode": null,
  "basePrice": null,
  "interestRate": null,
  "items": [
    {
      "title": "Pagamento Venda 01",
      "quantity": 1,
      "tangible": false,
      "unitPrice": 1000,
      "externalRef": ""
    }
  ],
  "customer": {
    "id": 12547087,
    "name": "Gustavo Dias Pinto",
    "email": "<EMAIL>",
    "phone": "11925889655",
    "birthdate": null,
    "createdAt": "2025-07-31T02:01:52.149Z",
    "externalRef": null,
    "document": {
      "type": "cpf",
      "number": "42804615898"
    },
    "address": null
  },
  "fee": {
    "netAmount": 785,
    "estimatedFee": 214,
    "fixedAmount": 175,
    "spreadPercent": 399,
    "currency": "BRL"
  },
  "splits": [
    {
      "amount": 1000,
      "netAmount": 785,
      "recipientId": 17696,
      "chargeProcessingFee": false
    }
  ],
  "refunds": [],
  "pix": {
    "qrcode": "00020126820014br.gov.bcb.pix2560pix.treeal.com/qr/v3/at/2caf6e52-9db9-45ca-b65b-1e9f3a1129d85204000053039865802BR5925PAG_DROP_TECNOLOGIA_E_PAG6008ITABORAI62070503***630440DF",
    "end2EndId": null,
    "receiptUrl": null,
    "expirationDate": "2025-08-02"
  },
  "boleto": null,
  "card": null,
  "refusedReason": null,
  "shipping": null,
  "delivery": null,
  "threeDS": null
}

INFORMACAO RESPONSES 

200
Objeto de resposta em caso de sucesso

400
Objeto de resposta em caso de falha

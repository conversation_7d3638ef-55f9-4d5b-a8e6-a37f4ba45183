/* Estilos Administrativos - Sistema Universal de Gateways */

/* Layout Principal */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
    color: #5a5c69;
}

/* Cards com Bordas Coloridas */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

/* Cards de Estatísticas */
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Texto e Cores */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-600 {
    color: #858796 !important;
}

.text-gray-500 {
    color: #b7b9cc !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Botões */
.btn {
    border-radius: 0.35rem;
    font-weight: 400;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
}

.btn-danger {
    background-color: #e74a3b;
    border-color: #e74a3b;
}

/* Tabelas */
.table {
    color: #5a5c69;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    background-color: #f8f9fc;
}

.table-responsive {
    border-radius: 0.35rem;
}

/* Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Formulários */
.form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    color: #6e707e;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
}

/* Alertas */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-weight: 400;
}

.alert-primary {
    background-color: #dae2f8;
    color: #2653d4;
}

.alert-success {
    background-color: #d4edda;
    color: #0f5132;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Modais */
.modal-content {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

/* Paginação */
.pagination .page-link {
    border: 1px solid #dddfeb;
    color: #5a5c69;
    padding: 0.5rem 0.75rem;
}

.pagination .page-link:hover {
    background-color: #eaecf4;
    border-color: #dddfeb;
    color: #5a5c69;
}

.pagination .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #858796;
}

/* Dropdown */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.35rem;
}

.dropdown-item {
    color: #3a3b45;
    font-weight: 400;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
    color: #3a3b45;
}

/* Progress Bars */
.progress {
    height: 1rem;
    border-radius: 0.35rem;
    background-color: #eaecf4;
}

.progress-bar {
    border-radius: 0.35rem;
}

/* Utilities */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem 0 rgba(58, 59, 69, 0.2) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Animações */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Status Indicators */
.status-online {
    color: #1cc88a;
}

.status-offline {
    color: #e74a3b;
}

.status-pending {
    color: #f6c23e;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bg-light {
        background-color: #2c2c54 !important;
    }
    
    .text-dark {
        color: #ffffff !important;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .dropdown {
        display: none !important;
    }
    
    .main {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

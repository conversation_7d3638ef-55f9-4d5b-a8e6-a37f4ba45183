<?php
/**
 * <PERSON>ript para corrigir senhas dos usuários
 * Execute este arquivo para resetar as senh<PERSON> padr<PERSON>
 */

require_once 'config/config.php';

echo "<h1>Correção de Senhas - Sistema Gateway</h1>";

try {
    $db = Database::getInstance();
    
    // Gerar hash correto para a senha "password"
    $passwordHash = password_hash('password', PASSWORD_DEFAULT);
    
    echo "<h2>Informações de Debug:</h2>";
    echo "<p><strong>Hash gerado:</strong> " . $passwordHash . "</p>";
    echo "<p><strong>Algoritmo:</strong> " . PASSWORD_DEFAULT . "</p>";
    echo "<p><strong>Verificação:</strong> " . (password_verify('password', $passwordHash) ? 'OK' : 'FALHA') . "</p>";
    
    // Verificar usuários existentes
    $users = $db->fetchAll("SELECT id, username, password FROM users");
    
    echo "<h2>Usuários Encontrados:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Hash Atual</th><th>Verificação</th></tr>";
    
    foreach ($users as $user) {
        $verification = password_verify('password', $user['password']) ? 'OK' : 'FALHA';
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
        echo "<td>" . htmlspecialchars(substr($user['password'], 0, 30)) . "...</td>";
        echo "<td style='color: " . ($verification === 'OK' ? 'green' : 'red') . ";'>" . $verification . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Atualizar senhas se necessário
    $updated = 0;
    foreach ($users as $user) {
        if (!password_verify('password', $user['password'])) {
            $db->update('users', 
                ['password' => $passwordHash], 
                'id = :id', 
                ['id' => $user['id']]
            );
            $updated++;
            echo "<p style='color: green;'>✅ Senha atualizada para usuário: " . htmlspecialchars($user['username']) . "</p>";
        }
    }
    
    if ($updated === 0) {
        echo "<p style='color: blue;'>ℹ️ Todas as senhas já estão corretas.</p>";
    } else {
        echo "<p style='color: green;'><strong>✅ {$updated} senha(s) foram atualizadas com sucesso!</strong></p>";
    }
    
    // Verificar novamente após atualização
    echo "<h2>Verificação Final:</h2>";
    $usersAfter = $db->fetchAll("SELECT id, username, password FROM users");
    
    foreach ($usersAfter as $user) {
        $verification = password_verify('password', $user['password']);
        echo "<p>";
        echo "<strong>" . htmlspecialchars($user['username']) . ":</strong> ";
        echo "<span style='color: " . ($verification ? 'green' : 'red') . ";'>";
        echo $verification ? "✅ Senha OK" : "❌ Senha com problema";
        echo "</span>";
        echo "</p>";
    }
    
    echo "<hr>";
    echo "<h2>Credenciais de Login:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>👨‍💼 Administrador:</h3>";
    echo "<p><strong>Usuário:</strong> admin</p>";
    echo "<p><strong>Senha:</strong> password</p>";
    echo "<p><strong>URL:</strong> <a href='login.php' target='_blank'>login.php</a></p>";
    echo "</div>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>👤 Cliente de Exemplo:</h3>";
    echo "<p><strong>Usuário:</strong> cliente1</p>";
    echo "<p><strong>Senha:</strong> password</p>";
    echo "<p><strong>URL:</strong> <a href='login.php' target='_blank'>login.php</a></p>";
    echo "</div>";
    
    // Teste de login programático
    echo "<h2>Teste de Login Programático:</h2>";
    
    $auth = new Auth();
    
    // Teste admin
    if ($auth->login('admin', 'password')) {
        echo "<p style='color: green;'>✅ Login admin funcionando!</p>";
        $auth->logout();
    } else {
        echo "<p style='color: red;'>❌ Falha no login admin</p>";
    }
    
    // Teste cliente
    if ($auth->login('cliente1', 'password')) {
        echo "<p style='color: green;'>✅ Login cliente funcionando!</p>";
        $auth->logout();
    } else {
        echo "<p style='color: red;'>❌ Falha no login cliente</p>";
    }
    
    echo "<hr>";
    echo "<p><strong>🔒 Importante:</strong> Remova este arquivo após usar por segurança.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Verifique se o banco de dados foi criado corretamente.</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

table {
    margin: 10px 0;
}

th, td {
    padding: 8px 12px;
    text-align: left;
}

th {
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}
</style>

<?php
/**
 * Verificação e Criação do Banco de Dados
 */

echo "<h1>🔍 Verificação do Banco de Dados</h1>";

// Configurações do banco
$host = 'localhost';
$user = 'root';
$pass = '';
$dbname = 'gateway_system';

try {
    echo "<h2>1. Testando Conexão com MySQL</h2>";
    
    // Conectar sem especificar banco
    $dsn = "mysql:host={$host};charset=utf8mb4";
    $pdo = new PDO($dsn, $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Conexão com MySQL estabelecida</p>";
    
    echo "<h2>2. Verificando se o Banco Existe</h2>";
    
    // Verificar se o banco existe
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbname}'");
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "<p style='color: green;'>✅ Banco '{$dbname}' existe</p>";
    } else {
        echo "<p style='color: red;'>❌ Banco '{$dbname}' não existe</p>";
        echo "<p>Criando banco...</p>";
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ Banco '{$dbname}' criado com sucesso</p>";
    }
    
    // Selecionar o banco
    $pdo->exec("USE `{$dbname}`");
    
    echo "<h2>3. Verificando Tabelas</h2>";
    
    $tables = ['users', 'gateway_configs', 'system_settings', 'transactions', 'transfers', 'system_logs', 'admin_fees'];
    $existingTables = [];
    
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existingTables[] = $row[0];
    }
    
    echo "<p><strong>Tabelas encontradas:</strong> " . (empty($existingTables) ? "Nenhuma" : implode(', ', $existingTables)) . "</p>";
    
    $missingTables = array_diff($tables, $existingTables);
    
    if (empty($missingTables)) {
        echo "<p style='color: green;'>✅ Todas as tabelas necessárias existem</p>";
    } else {
        echo "<p style='color: red;'>❌ Tabelas faltando: " . implode(', ', $missingTables) . "</p>";
        echo "<p>Executando script SQL...</p>";
        
        // Executar o script SQL
        if (file_exists('database.sql')) {
            $sql = file_get_contents('database.sql');
            
            // Dividir em comandos individuais
            $commands = array_filter(array_map('trim', explode(';', $sql)));
            
            $executed = 0;
            $errors = 0;
            
            foreach ($commands as $command) {
                if (!empty($command) && !preg_match('/^--/', $command)) {
                    try {
                        $pdo->exec($command);
                        $executed++;
                    } catch (PDOException $e) {
                        $errors++;
                        echo "<p style='color: orange;'>⚠️ Erro no comando: " . htmlspecialchars(substr($command, 0, 50)) . "... - " . $e->getMessage() . "</p>";
                    }
                }
            }
            
            echo "<p style='color: green;'>✅ Executados {$executed} comandos SQL com {$errors} erros</p>";
        } else {
            echo "<p style='color: red;'>❌ Arquivo database.sql não encontrado</p>";
        }
    }
    
    echo "<h2>4. Verificando Dados dos Usuários</h2>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $userCount = $stmt->fetch()['total'];
        
        echo "<p><strong>Total de usuários:</strong> {$userCount}</p>";
        
        if ($userCount == 0) {
            echo "<p style='color: orange;'>⚠️ Nenhum usuário encontrado. Inserindo usuários padrão...</p>";
            
            // Inserir usuários padrão
            $passwordHash = password_hash('password', PASSWORD_DEFAULT);
            
            $users = [
                ['admin', '<EMAIL>', $passwordHash, 'Administrador', 'admin'],
                ['cliente1', '<EMAIL>', $passwordHash, 'Cliente Exemplo', 'client']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, user_type) VALUES (?, ?, ?, ?, ?)");
            
            foreach ($users as $user) {
                try {
                    $stmt->execute($user);
                    echo "<p style='color: green;'>✅ Usuário '{$user[0]}' criado</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>❌ Erro ao criar usuário '{$user[0]}': " . $e->getMessage() . "</p>";
                }
            }
        } else {
            // Mostrar usuários existentes
            $stmt = $pdo->query("SELECT id, username, email, user_type, status FROM users");
            $users = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Tipo</th><th>Status</th></tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['user_type']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Erro ao verificar usuários: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Teste de Login</h2>";
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? AND status = 'active'");
        $stmt->execute(['admin']);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<p style='color: green;'>✅ Usuário admin encontrado</p>";
            
            $passwordCheck = password_verify('password', $user['password']);
            echo "<p>Verificação de senha: " . ($passwordCheck ? "✅ OK" : "❌ FALHA") . "</p>";
            
            if (!$passwordCheck) {
                echo "<p style='color: orange;'>⚠️ Corrigindo senha...</p>";
                $newHash = password_hash('password', PASSWORD_DEFAULT);
                $updateStmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                $updateStmt->execute([$newHash, $user['id']]);
                echo "<p style='color: green;'>✅ Senha corrigida</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Usuário admin não encontrado</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Erro no teste de login: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. Criando Arquivo de Configuração</h2>";
    
    $configContent = "<?php
// Configurações do Banco de Dados
define('DB_HOST', '{$host}');
define('DB_NAME', '{$dbname}');
define('DB_USER', '{$user}');
define('DB_PASS', '{$pass}');
define('DB_CHARSET', 'utf8mb4');

// Configurações do Sistema
define('SYSTEM_NAME', 'Gateway Universal');
define('SYSTEM_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/novo/');
define('ADMIN_EMAIL', '<EMAIL>');

// Configurações de Segurança
define('SESSION_NAME', 'gateway_session');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_HASH_ALGO', PASSWORD_DEFAULT);

// Configurações de Log
define('LOG_LEVEL', 'info');
define('LOG_FILE_PATH', __DIR__ . '/../logs/');

// Configurações de API
define('API_TIMEOUT', 30);
define('API_USER_AGENT', 'Gateway-Universal/1.0');

// Configurações de Timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações de Erro
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', LOG_FILE_PATH . 'php_errors.log');

// Autoload de classes
spl_autoload_register(function (\$class) {
    \$paths = [
        __DIR__ . '/../classes/',
        __DIR__ . '/../includes/',
    ];
    
    foreach (\$paths as \$path) {
        \$file = \$path . \$class . '.php';
        if (file_exists(\$file)) {
            require_once \$file;
            return;
        }
    }
});

// Iniciar sessão se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Função para gerar token CSRF
function generateCSRFToken() {
    if (!isset(\$_SESSION[CSRF_TOKEN_NAME])) {
        \$_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return \$_SESSION[CSRF_TOKEN_NAME];
}

// Função para verificar token CSRF
function verifyCSRFToken(\$token) {
    return isset(\$_SESSION[CSRF_TOKEN_NAME]) && hash_equals(\$_SESSION[CSRF_TOKEN_NAME], \$token);
}

// Função para sanitizar entrada
function sanitizeInput(\$input) {
    if (is_array(\$input)) {
        return array_map('sanitizeInput', \$input);
    }
    return htmlspecialchars(trim(\$input), ENT_QUOTES, 'UTF-8');
}

// Função para validar email
function isValidEmail(\$email) {
    return filter_var(\$email, FILTER_VALIDATE_EMAIL) !== false;
}

// Função para formatar valor monetário
function formatMoney(\$value, \$currency = 'BRL') {
    return number_format(\$value, 2, ',', '.');
}

// Função para converter centavos para reais
function centavosToReais(\$centavos) {
    return \$centavos / 100;
}

// Função para converter reais para centavos
function reaisToCentavos(\$reais) {
    return \$reais * 100;
}

// Função para gerar ID único
function generateUniqueId(\$prefix = '') {
    return \$prefix . uniqid() . bin2hex(random_bytes(4));
}

// Função para log de debug (apenas em desenvolvimento)
function debugLog(\$message, \$data = null) {
    if (defined('DEBUG') && DEBUG === true) {
        \$log = date('Y-m-d H:i:s') . ' - ' . \$message;
        if (\$data !== null) {
            \$log .= ' - Data: ' . json_encode(\$data);
        }
        error_log(\$log . PHP_EOL, 3, LOG_FILE_PATH . 'debug.log');
    }
}

// Verificar se as pastas necessárias existem
\$requiredDirs = [
    __DIR__ . '/../logs',
    __DIR__ . '/../uploads',
    __DIR__ . '/../temp'
];

foreach (\$requiredDirs as \$dir) {
    if (!is_dir(\$dir)) {
        mkdir(\$dir, 0755, true);
    }
}

// Configurações específicas do ambiente
if (file_exists(__DIR__ . '/local_config.php')) {
    require_once __DIR__ . '/local_config.php';
}
?>";
    
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    file_put_contents('config/config.php', $configContent);
    echo "<p style='color: green;'>✅ Arquivo config/config.php criado</p>";
    
    echo "<hr>";
    echo "<h2>✅ Resumo Final</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>✅ Banco de dados configurado</strong></p>";
    echo "<p><strong>✅ Tabelas criadas</strong></p>";
    echo "<p><strong>✅ Usuários inseridos</strong></p>";
    echo "<p><strong>✅ Configuração salva</strong></p>";
    echo "</div>";
    
    echo "<h3>🔑 Credenciais de Login:</h3>";
    echo "<p><strong>Admin:</strong> admin / password</p>";
    echo "<p><strong>Cliente:</strong> cliente1 / password</p>";
    echo "<p><strong>URL:</strong> <a href='login.php'>login.php</a></p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ Erro de Conexão</h2>";
    echo "<p><strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Código:</strong> " . $e->getCode() . "</p>";
    
    echo "<h3>🔧 Possíveis Soluções:</h3>";
    echo "<ul>";
    echo "<li>Verifique se o XAMPP está rodando</li>";
    echo "<li>Verifique se o MySQL está ativo</li>";
    echo "<li>Confirme as credenciais do banco (usuário: root, senha: vazia)</li>";
    echo "<li>Tente acessar o phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

table {
    margin: 10px 0;
    width: 100%;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}
</style>

<?php
// Configurações do Banco de Dados
define('DB_HOST', 'localhost');
define('DB_NAME', 'gateway_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Configurações do Sistema
define('SYSTEM_NAME', 'Gateway Universal');
define('SYSTEM_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/novo/');
define('ADMIN_EMAIL', '<EMAIL>');

// Configurações de Segurança
define('SESSION_NAME', 'gateway_session');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_HASH_ALGO', PASSWORD_DEFAULT);

// Configurações de Log
define('LOG_LEVEL', 'info');
define('LOG_FILE_PATH', __DIR__ . '/../logs/');

// Configurações de API
define('API_TIMEOUT', 30);
define('API_USER_AGENT', 'Gateway-Universal/1.0');

// Configurações de Timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações de Erro
error_reporting(E_ALL);
ini_set('display_errors', 1); // Temporariamente ativado para debug
ini_set('log_errors', 1);
ini_set('error_log', LOG_FILE_PATH . 'php_errors.log');

// Autoload de classes
spl_autoload_register(function ($class) {
    $paths = [
        __DIR__ . '/../classes/',
        __DIR__ . '/../includes/',
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Iniciar sessão se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Função para gerar token CSRF
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// Função para verificar token CSRF
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// Função para sanitizar entrada
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Função para validar email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Função para formatar valor monetário
function formatMoney($value, $currency = 'BRL') {
    return number_format($value, 2, ',', '.');
}

// Função para converter centavos para reais
function centavosToReais($centavos) {
    return $centavos / 100;
}

// Função para converter reais para centavos
function reaisToCentavos($reais) {
    return $reais * 100;
}

// Função para gerar ID único
function generateUniqueId($prefix = '') {
    return $prefix . uniqid() . bin2hex(random_bytes(4));
}

// Função para log de debug (apenas em desenvolvimento)
function debugLog($message, $data = null) {
    if (defined('DEBUG') && DEBUG === true) {
        $log = date('Y-m-d H:i:s') . ' - ' . $message;
        if ($data !== null) {
            $log .= ' - Data: ' . json_encode($data);
        }
        error_log($log . PHP_EOL, 3, LOG_FILE_PATH . 'debug.log');
    }
}

// Verificar se as pastas necessárias existem
$requiredDirs = [
    __DIR__ . '/../logs',
    __DIR__ . '/../uploads',
    __DIR__ . '/../temp'
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Configurações específicas do ambiente
if (file_exists(__DIR__ . '/local_config.php')) {
    require_once __DIR__ . '/local_config.php';
}
?>
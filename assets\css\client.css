/* Estilos do Cliente - Sistema Universal de Gateways */

/* Layout Principal */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
    color: #5a5c69;
}

/* Navbar do Cliente */
.navbar-dark.bg-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
}

/* Cards com Bordas Coloridas */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

/* Cards */
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Cores do Texto */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-600 {
    color: #858796 !important;
}

.text-gray-500 {
    color: #b7b9cc !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Botões */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-transform: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
    border: none;
}

/* Formulários */
.form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    color: #6e707e;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
}

/* Tabelas */
.table {
    color: #5a5c69;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    background-color: #f8f9fc;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.table-responsive {
    border-radius: 0.35rem;
}

/* Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

/* Alertas */
.alert {
    border: none;
    border-radius: 0.35rem;
    font-weight: 400;
    padding: 1rem 1.25rem;
}

.alert-primary {
    background: linear-gradient(135deg, #dae2f8 0%, #c3d3f7 100%);
    color: #2653d4;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #0f5132;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #055160;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #664d03;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Modais */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    background: linear-gradient(135deg, #f8f9fc 0%, #eaecf4 100%);
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

/* Saldo Display */
.balance-display {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    margin-bottom: 2rem;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.balance-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* QR Code Display */
.qr-code-container {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.qr-code-container img {
    max-width: 300px;
    width: 100%;
    height: auto;
    border: 3px solid #e3e6f0;
    border-radius: 0.5rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-online .status-dot {
    background-color: #1cc88a;
}

.status-offline .status-dot {
    background-color: #e74a3b;
}

.status-pending .status-dot {
    background-color: #f6c23e;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Animações */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utilities */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.rounded-lg {
    border-radius: 0.5rem !important;
}

/* Responsive */
@media (max-width: 768px) {
    .balance-amount {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .qr-code-container {
        padding: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .card {
        background-color: #2d2d2d;
        border-color: #404040;
    }
    
    .table {
        color: #e0e0e0;
    }
    
    .table th {
        background-color: #404040;
        color: #e0e0e0;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn:not(.btn-print),
    .dropdown {
        display: none !important;
    }
    
    .main {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .qr-code-container {
        page-break-inside: avoid;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #224abe 0%, #1e3a8a 100%);
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #4e73df;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .card {
        border: 2px solid #000;
    }
}

Buscar Saque
GET
https://api.pagdrop.com/v1/transfers/{id}

A rota GET /transfers/{id}permite recuperar os detalhes de um saque pelo seu ID.

Header Params
x-withdraw-keystringrequired
Chave de saque externo vinculada ao usuário.

Path Params
idint32required
ID da tranferência

<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/transfers/165595",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "GET",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw==",
    "x-withdraw-key: wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m"
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}



RESPONSE REAL DA API
200

{
  "id": 165595,
  "tenantId": "7a45cd92-63f7-4e31-92ed-5ef13b9333fa",
  "companyId": 17696,
  "amount": 100,
  "netAmount": 98,
  "currency": "BRL",
  "fee": 2,
  "method": "fiat",
  "status": "PROCESSING",
  "externalRef": null,
  "isExternal": true,
  "pixKey": "63999288544",
  "pixKeyType": "phone",
  "pixEnd2EndId": null,
  "cryptoWallet": null,
  "cryptoNetwork": null,
  "cryptoAddress": null,
  "description": "FAILED: WAITING RETRY",
  "metadata": "{\n  \"isExternal\": true,\n  \"secretKey\": true,\n  \"withdrawKey\": true,\n  \"systemKey\": false,\n  \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n  \"hostname\": \"api.pagdrop.com\",\n  \"ip\": \"*************\",\n  \"request\": {\n    \"method\": \"fiat\",\n    \"amount\": 100,\n    \"netPayout\": false,\n    \"pixKey\": \"63999288544\",\n    \"pixKeyType\": \"phone\"\n  }\n}",
  "postbackUrl": null,
  "history": [
    {
      "date": "2025-07-31T02:14:29.904Z",
      "message": "Request failed with status code 500"
    }
  ],
  "transferredAt": null,
  "processedAt": null,
  "canceledAt": null,
  "createdAt": "2025-07-31T02:14:16.091Z",
  "updatedAt": "2025-07-31T02:14:29.908Z"
}


INFORMACAO RESPONSES

200
Objeto de resposta em caso de sucesso na consulta de um saque

400
Objeto de resposta em caso de falha






/**
 * JavaScript para Painel Administrativo
 * Sistema Universal de Gateways
 */

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

/**
 * Inicializar funcionalidades do admin
 */
function initializeAdmin() {
    // Inicializar tooltips
    initializeTooltips();
    
    // Inicializar confirmações
    initializeConfirmations();
    
    // Inicializar auto-refresh
    initializeAutoRefresh();
    
    // Inicializar formulários
    initializeForms();
    
    // Inicializar tabelas
    initializeTables();
    
    // Inicializar notificações
    initializeNotifications();
}

/**
 * Inicializar tooltips do Bootstrap
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Inicializar confirmações para ações perigosas
 */
function initializeConfirmations() {
    // Confirmação para exclusões
    document.querySelectorAll('.btn-delete, .delete-action').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const message = this.getAttribute('data-message') || 'Tem certeza que deseja excluir este item?';
            const url = this.getAttribute('href') || this.getAttribute('data-url');
            
            if (confirm(message)) {
                if (url) {
                    window.location.href = url;
                } else {
                    this.closest('form')?.submit();
                }
            }
        });
    });
    
    // Confirmação para ações importantes
    document.querySelectorAll('.btn-confirm, .confirm-action').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            const message = this.getAttribute('data-message') || 'Tem certeza que deseja realizar esta ação?';
            
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
}

/**
 * Inicializar auto-refresh para páginas específicas
 */
function initializeAutoRefresh() {
    const autoRefreshElements = document.querySelectorAll('[data-auto-refresh]');
    
    autoRefreshElements.forEach(function(element) {
        const interval = parseInt(element.getAttribute('data-auto-refresh')) * 1000;
        
        if (interval > 0) {
            setInterval(function() {
                location.reload();
            }, interval);
        }
    });
}

/**
 * Inicializar funcionalidades de formulários
 */
function initializeForms() {
    // Validação em tempo real
    document.querySelectorAll('form[data-validate]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
        });
    });
    
    // Máscaras para campos
    initializeInputMasks();
    
    // Auto-save para formulários longos
    initializeAutoSave();
}

/**
 * Validar formulário
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            showFieldError(field, 'Este campo é obrigatório');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Validações específicas
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(function(field) {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Email inválido');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Mostrar erro em campo
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * Limpar erro de campo
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Validar email
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Inicializar máscaras de entrada
 */
function initializeInputMasks() {
    // Máscara para CPF
    document.querySelectorAll('.cpf-mask').forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = formatCPF(this.value);
        });
    });
    
    // Máscara para CNPJ
    document.querySelectorAll('.cnpj-mask').forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = formatCNPJ(this.value);
        });
    });
    
    // Máscara para telefone
    document.querySelectorAll('.phone-mask').forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = formatPhone(this.value);
        });
    });
    
    // Máscara para dinheiro
    document.querySelectorAll('.money-mask').forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = formatMoney(this.value);
        });
    });
}

/**
 * Formatar CPF
 */
function formatCPF(value) {
    return value
        .replace(/\D/g, '')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d{1,2})/, '$1-$2')
        .replace(/(-\d{2})\d+?$/, '$1');
}

/**
 * Formatar CNPJ
 */
function formatCNPJ(value) {
    return value
        .replace(/\D/g, '')
        .replace(/(\d{2})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1/$2')
        .replace(/(\d{4})(\d{1,2})/, '$1-$2')
        .replace(/(-\d{2})\d+?$/, '$1');
}

/**
 * Formatar telefone
 */
function formatPhone(value) {
    return value
        .replace(/\D/g, '')
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .replace(/(\d{4})-(\d)(\d{4})/, '$1$2-$3')
        .replace(/(-\d{4})\d+?$/, '$1');
}

/**
 * Formatar dinheiro
 */
function formatMoney(value) {
    value = value.replace(/\D/g, '');
    value = (value / 100).toFixed(2) + '';
    value = value.replace('.', ',');
    value = value.replace(/(\d)(\d{3})(\d{3}),/g, '$1.$2.$3,');
    value = value.replace(/(\d)(\d{3}),/g, '$1.$2,');
    return 'R$ ' + value;
}

/**
 * Inicializar auto-save
 */
function initializeAutoSave() {
    const autoSaveForms = document.querySelectorAll('form[data-auto-save]');
    
    autoSaveForms.forEach(function(form) {
        const interval = parseInt(form.getAttribute('data-auto-save')) * 1000 || 30000;
        
        setInterval(function() {
            saveFormData(form);
        }, interval);
        
        // Restaurar dados salvos
        restoreFormData(form);
    });
}

/**
 * Salvar dados do formulário
 */
function saveFormData(form) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    const formId = form.id || 'form_' + Date.now();
    localStorage.setItem('autosave_' + formId, JSON.stringify(data));
}

/**
 * Restaurar dados do formulário
 */
function restoreFormData(form) {
    const formId = form.id || 'form_' + Date.now();
    const savedData = localStorage.getItem('autosave_' + formId);
    
    if (savedData) {
        const data = JSON.parse(savedData);
        
        for (let [key, value] of Object.entries(data)) {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = value;
            }
        }
    }
}

/**
 * Inicializar funcionalidades de tabelas
 */
function initializeTables() {
    // Ordenação de tabelas
    document.querySelectorAll('table[data-sortable] th[data-sort]').forEach(function(th) {
        th.style.cursor = 'pointer';
        th.addEventListener('click', function() {
            sortTable(this);
        });
    });
    
    // Filtros de tabela
    document.querySelectorAll('input[data-table-filter]').forEach(function(input) {
        input.addEventListener('input', function() {
            filterTable(this);
        });
    });
}

/**
 * Ordenar tabela
 */
function sortTable(th) {
    const table = th.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const column = Array.from(th.parentNode.children).indexOf(th);
    const isAsc = th.classList.contains('sort-asc');
    
    // Remover classes de ordenação de todas as colunas
    th.parentNode.querySelectorAll('th').forEach(function(header) {
        header.classList.remove('sort-asc', 'sort-desc');
    });
    
    // Adicionar classe de ordenação
    th.classList.add(isAsc ? 'sort-desc' : 'sort-asc');
    
    // Ordenar linhas
    rows.sort(function(a, b) {
        const aText = a.children[column].textContent.trim();
        const bText = b.children[column].textContent.trim();
        
        if (isAsc) {
            return bText.localeCompare(aText, undefined, { numeric: true });
        } else {
            return aText.localeCompare(bText, undefined, { numeric: true });
        }
    });
    
    // Reordenar no DOM
    rows.forEach(function(row) {
        tbody.appendChild(row);
    });
}

/**
 * Filtrar tabela
 */
function filterTable(input) {
    const tableId = input.getAttribute('data-table-filter');
    const table = document.getElementById(tableId);
    const filter = input.value.toLowerCase();
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(function(row) {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(filter) ? '' : 'none';
    });
}

/**
 * Inicializar sistema de notificações
 */
function initializeNotifications() {
    // Verificar notificações a cada 30 segundos
    setInterval(checkNotifications, 30000);
    
    // Auto-hide para alertas
    document.querySelectorAll('.alert[data-auto-hide]').forEach(function(alert) {
        const delay = parseInt(alert.getAttribute('data-auto-hide')) * 1000;
        
        setTimeout(function() {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            
            setTimeout(function() {
                alert.remove();
            }, 500);
        }, delay);
    });
}

/**
 * Verificar novas notificações
 */
function checkNotifications() {
    fetch('api/notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.count > 0) {
                updateNotificationBadge(data.count);
            }
        })
        .catch(error => {
            console.error('Erro ao verificar notificações:', error);
        });
}

/**
 * Atualizar badge de notificações
 */
function updateNotificationBadge(count) {
    const badge = document.querySelector('.badge-counter');
    if (badge) {
        badge.textContent = count > 99 ? '99+' : count;
        badge.style.display = count > 0 ? 'block' : 'none';
    }
}

/**
 * Mostrar notificação toast
 */
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remover após esconder
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

/**
 * Criar container de toasts
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1055';
    
    document.body.appendChild(container);
    return container;
}

/**
 * Utilitários globais
 */
window.AdminUtils = {
    showToast: showToast,
    validateForm: validateForm,
    formatCPF: formatCPF,
    formatCNPJ: formatCNPJ,
    formatPhone: formatPhone,
    formatMoney: formatMoney
};

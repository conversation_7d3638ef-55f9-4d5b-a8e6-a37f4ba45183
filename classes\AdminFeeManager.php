<?php
/**
 * Gerenciador de Taxas Administrativas Automáticas
 * Sistema Universal de Gateways
 */

class AdminFeeManager {
    private $db;
    private $logger;
    private $gatewayAPI;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logger = new Logger();
    }
    
    /**
     * Processar taxa administrativa para uma transação
     */
    public function processAdminFee($transactionId, $userId) {
        try {
            $this->db->beginTransaction();
            
            // Buscar dados da transação
            $transaction = $this->getTransactionData($transactionId);
            if (!$transaction) {
                throw new Exception('Transação não encontrada');
            }
            
            // Verificar se já foi processada
            if ($this->feeAlreadyProcessed($transactionId)) {
                $this->logger->log('warning', 'Taxa já processada para esta transação', ['transaction_id' => $transactionId]);
                return false;
            }
            
            // Buscar configuração do usuário
            $config = $this->getUserConfig($userId);
            if (!$config) {
                throw new Exception('Configuração do usuário não encontrada');
            }
            
            // Calcular taxa
            $feeAmount = $this->calculateFee($transaction['amount'], $config['admin_fee_percentage']);
            
            if ($feeAmount <= 0) {
                $this->logger->log('info', 'Taxa zerada, não processando', ['transaction_id' => $transactionId]);
                $this->db->commit();
                return false;
            }
            
            // Obter chave PIX de destino
            $pixDestination = $this->getAdminPixKey();
            if (!$pixDestination) {
                throw new Exception('Chave PIX administrativa não configurada');
            }
            
            // Registrar taxa no banco
            $feeId = $this->recordAdminFee($transactionId, $userId, $transaction['amount'], $config['admin_fee_percentage'], $feeAmount, $pixDestination['key']);
            
            // Enviar PIX da taxa
            $pixResult = $this->sendAdminFeePix($feeId, $feeAmount, $pixDestination, $transactionId);
            
            // Atualizar transação com dados da taxa
            $this->updateTransactionFee($transactionId, $feeAmount, $config['admin_fee_percentage']);
            
            $this->db->commit();
            
            $this->logger->logAdminFee('processed', $feeId, [
                'transaction_id' => $transactionId,
                'fee_amount' => $feeAmount,
                'fee_percentage' => $config['admin_fee_percentage'],
                'pix_status' => $pixResult['status']
            ], $userId);
            
            return [
                'success' => true,
                'fee_id' => $feeId,
                'fee_amount' => $feeAmount,
                'pix_status' => $pixResult['status']
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->logger->log('error', 'Erro ao processar taxa administrativa: ' . $e->getMessage(), ['transaction_id' => $transactionId]);
            throw $e;
        }
    }
    
    /**
     * Calcular valor da taxa
     */
    private function calculateFee($amount, $percentage) {
        return round(($amount * $percentage) / 100, 2);
    }
    
    /**
     * Buscar dados da transação
     */
    private function getTransactionData($transactionId) {
        return $this->db->fetch(
            "SELECT * FROM transactions WHERE id = :id AND status IN ('paid', 'approved')",
            ['id' => $transactionId]
        );
    }
    
    /**
     * Verificar se taxa já foi processada
     */
    private function feeAlreadyProcessed($transactionId) {
        return $this->db->exists('admin_fees', 'transaction_id = :transaction_id', ['transaction_id' => $transactionId]);
    }
    
    /**
     * Buscar configuração do usuário
     */
    private function getUserConfig($userId) {
        return $this->db->fetch(
            "SELECT * FROM gateway_configs WHERE user_id = :user_id AND status = 'active'",
            ['user_id' => $userId]
        );
    }
    
    /**
     * Obter chave PIX administrativa
     */
    private function getAdminPixKey() {
        $pixKey = $this->db->fetch(
            "SELECT setting_value as key FROM system_settings WHERE setting_key = 'admin_pix_key'"
        );
        
        $pixKeyType = $this->db->fetch(
            "SELECT setting_value as type FROM system_settings WHERE setting_key = 'admin_pix_key_type'"
        );
        
        if (!$pixKey || empty($pixKey['key'])) {
            return null;
        }
        
        return [
            'key' => $pixKey['key'],
            'type' => $pixKeyType['type'] ?? 'email'
        ];
    }
    
    /**
     * Registrar taxa no banco
     */
    private function recordAdminFee($transactionId, $userId, $originalAmount, $feePercentage, $feeAmount, $pixDestination) {
        return $this->db->insert('admin_fees', [
            'transaction_id' => $transactionId,
            'user_id' => $userId,
            'original_amount' => $originalAmount,
            'fee_percentage' => $feePercentage,
            'fee_amount' => $feeAmount,
            'pix_key_destination' => $pixDestination,
            'pix_status' => 'pending'
        ]);
    }
    
    /**
     * Enviar PIX da taxa administrativa
     */
    private function sendAdminFeePix($feeId, $feeAmount, $pixDestination, $transactionId) {
        try {
            // Buscar um usuário admin para usar suas credenciais
            $adminUser = $this->db->fetch("SELECT id FROM users WHERE user_type = 'admin' LIMIT 1");
            if (!$adminUser) {
                throw new Exception('Usuário admin não encontrado');
            }
            
            // Inicializar API com credenciais admin
            $this->gatewayAPI = new GatewayAPI($adminUser['id']);
            
            // Preparar dados do PIX
            $pixData = [
                'method' => 'fiat',
                'amount' => reaisToCentavos($feeAmount),
                'netPayout' => false,
                'pixKey' => $pixDestination['key'],
                'pixKeyType' => $pixDestination['type']
            ];
            
            // Enviar PIX
            $result = $this->gatewayAPI->createTransfer($pixData);
            
            if ($result['success']) {
                // Atualizar status da taxa
                $this->db->update('admin_fees', [
                    'pix_status' => 'sent',
                    'pix_response' => json_encode($result['data']),
                    'sent_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $feeId]);
                
                return ['status' => 'sent', 'data' => $result['data']];
            } else {
                throw new Exception('Falha ao enviar PIX');
            }
            
        } catch (Exception $e) {
            // Atualizar status como falha
            $this->db->update('admin_fees', [
                'pix_status' => 'failed',
                'pix_response' => json_encode(['error' => $e->getMessage()])
            ], 'id = :id', ['id' => $feeId]);
            
            $this->logger->log('error', 'Falha ao enviar PIX da taxa', [
                'fee_id' => $feeId,
                'error' => $e->getMessage()
            ]);
            
            return ['status' => 'failed', 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Atualizar transação com dados da taxa
     */
    private function updateTransactionFee($transactionId, $feeAmount, $feePercentage) {
        $transaction = $this->db->fetch("SELECT amount FROM transactions WHERE id = :id", ['id' => $transactionId]);
        $netAmount = $transaction['amount'] - $feeAmount;
        
        $this->db->update('transactions', [
            'admin_fee_amount' => $feeAmount,
            'admin_fee_percentage' => $feePercentage,
            'net_amount' => $netAmount
        ], 'id = :id', ['id' => $transactionId]);
    }
    
    /**
     * Processar taxas pendentes (para execução via cron)
     */
    public function processPendingFees() {
        try {
            // Buscar transações pagas sem taxa processada
            $transactions = $this->db->fetchAll("
                SELECT t.id, t.user_id 
                FROM transactions t 
                LEFT JOIN admin_fees af ON t.id = af.transaction_id 
                WHERE t.status IN ('paid', 'approved') 
                AND af.id IS NULL 
                AND t.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                LIMIT 50
            ");
            
            $processed = 0;
            $errors = 0;
            
            foreach ($transactions as $transaction) {
                try {
                    $this->processAdminFee($transaction['id'], $transaction['user_id']);
                    $processed++;
                } catch (Exception $e) {
                    $errors++;
                    $this->logger->log('error', 'Erro ao processar taxa pendente', [
                        'transaction_id' => $transaction['id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            $this->logger->log('info', 'Processamento de taxas pendentes concluído', [
                'processed' => $processed,
                'errors' => $errors,
                'total_found' => count($transactions)
            ]);
            
            return ['processed' => $processed, 'errors' => $errors];
            
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro no processamento de taxas pendentes: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Retentar envio de PIX falhados
     */
    public function retryFailedPix() {
        try {
            $failedFees = $this->db->fetchAll("
                SELECT * FROM admin_fees 
                WHERE pix_status = 'failed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                LIMIT 20
            ");
            
            $retried = 0;
            $success = 0;
            
            foreach ($failedFees as $fee) {
                try {
                    $pixDestination = [
                        'key' => $fee['pix_key_destination'],
                        'type' => 'email' // Assumir email como padrão
                    ];
                    
                    $result = $this->sendAdminFeePix($fee['id'], $fee['fee_amount'], $pixDestination, $fee['transaction_id']);
                    
                    $retried++;
                    if ($result['status'] === 'sent') {
                        $success++;
                    }
                    
                } catch (Exception $e) {
                    $this->logger->log('error', 'Erro ao retentar PIX', [
                        'fee_id' => $fee['id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            $this->logger->log('info', 'Retry de PIX falhados concluído', [
                'retried' => $retried,
                'success' => $success,
                'total_found' => count($failedFees)
            ]);
            
            return ['retried' => $retried, 'success' => $success];
            
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro no retry de PIX falhados: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Obter relatório de taxas
     */
    public function getFeesReport($userId = null, $dateFrom = null, $dateTo = null) {
        $where = ['1=1'];
        $params = [];
        
        if ($userId) {
            $where[] = 'af.user_id = :user_id';
            $params['user_id'] = $userId;
        }
        
        if ($dateFrom) {
            $where[] = 'af.created_at >= :date_from';
            $params['date_from'] = $dateFrom . ' 00:00:00';
        }
        
        if ($dateTo) {
            $where[] = 'af.created_at <= :date_to';
            $params['date_to'] = $dateTo . ' 23:59:59';
        }
        
        $whereClause = implode(' AND ', $where);
        
        return $this->db->fetchAll("
            SELECT af.*, t.gateway_transaction_id, u.username, u.full_name
            FROM admin_fees af
            JOIN transactions t ON af.transaction_id = t.id
            JOIN users u ON af.user_id = u.id
            WHERE {$whereClause}
            ORDER BY af.created_at DESC
        ", $params);
    }
    
    /**
     * Obter estatísticas de taxas
     */
    public function getFeesStats($userId = null, $period = '30 days') {
        $where = $userId ? 'WHERE user_id = :user_id' : '';
        $params = $userId ? ['user_id' => $userId] : [];
        
        return $this->db->fetch("
            SELECT 
                COUNT(*) as total_fees,
                SUM(fee_amount) as total_amount,
                SUM(CASE WHEN pix_status = 'sent' THEN fee_amount ELSE 0 END) as sent_amount,
                SUM(CASE WHEN pix_status = 'failed' THEN fee_amount ELSE 0 END) as failed_amount,
                COUNT(CASE WHEN pix_status = 'sent' THEN 1 END) as sent_count,
                COUNT(CASE WHEN pix_status = 'failed' THEN 1 END) as failed_count,
                COUNT(CASE WHEN pix_status = 'pending' THEN 1 END) as pending_count
            FROM admin_fees 
            {$where}
            AND created_at >= DATE_SUB(NOW(), INTERVAL {$period})
        ", $params);
    }
}
?>

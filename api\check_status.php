<?php
/**
 * API para verificar status de transações/transferências
 * Sistema Universal de Gateways
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();

// Verificar se está logado
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit;
}

$type = $_GET['type'] ?? '';
$id = $_GET['id'] ?? '';

if (empty($type) || empty($id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Parâmetros inválidos']);
    exit;
}

try {
    $db = Database::getInstance();
    $userId = $auth->getUserId();
    
    if ($type === 'transaction') {
        // Verificar se o usuário pode acessar esta transação
        $where = $auth->isAdmin() ? 'id = :id' : 'id = :id AND user_id = :user_id';
        $params = $auth->isAdmin() ? ['id' => $id] : ['id' => $id, 'user_id' => $userId];
        
        $transaction = $db->fetch("SELECT status FROM transactions WHERE {$where}", $params);
        
        if (!$transaction) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Transação não encontrada']);
            exit;
        }
        
        echo json_encode([
            'success' => true,
            'status' => $transaction['status']
        ]);
        
    } elseif ($type === 'transfer') {
        // Verificar se o usuário pode acessar esta transferência
        $where = $auth->isAdmin() ? 'id = :id' : 'id = :id AND user_id = :user_id';
        $params = $auth->isAdmin() ? ['id' => $id] : ['id' => $id, 'user_id' => $userId];
        
        $transfer = $db->fetch("SELECT status FROM transfers WHERE {$where}", $params);
        
        if (!$transfer) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Transferência não encontrada']);
            exit;
        }
        
        echo json_encode([
            'success' => true,
            'status' => $transfer['status']
        ]);
        
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Tipo inválido']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro interno do servidor',
        'error' => $e->getMessage()
    ]);
}
?>

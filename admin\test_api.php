<?php
require_once '../config/config.php';

$auth = new Auth();
$auth->requireAdmin();

$db = Database::getInstance();
$testResults = [];
$selectedUserId = $_GET['user_id'] ?? null;

// Obter lista de clientes para teste
$clients = $db->fetchAll("
    SELECT u.id, u.username, u.full_name, gc.status as gateway_status 
    FROM users u 
    LEFT JOIN gateway_configs gc ON u.id = gc.user_id 
    WHERE u.user_type = 'client'
    ORDER BY u.username
");

// Executar teste se usuário foi selecionado
if ($selectedUserId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $gatewayAPI = new GatewayAPI($selectedUserId);
        
        // Teste 1: Obter saldo
        try {
            $balance = $gatewayAPI->getBalance();
            $testResults['balance'] = [
                'success' => true,
                'data' => $balance,
                'message' => 'Saldo obtido com sucesso'
            ];
        } catch (Exception $e) {
            $testResults['balance'] = [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erro ao obter saldo'
            ];
        }
        
        // Teste 2: Listar transações
        try {
            $transactions = $gatewayAPI->getTransactions(1, 5);
            $testResults['transactions'] = [
                'success' => true,
                'data' => $transactions,
                'message' => 'Transações listadas com sucesso'
            ];
        } catch (Exception $e) {
            $testResults['transactions'] = [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erro ao listar transações'
            ];
        }
        
        // Teste 3: Obter dados da empresa
        try {
            $company = $gatewayAPI->getCompanyData();
            $testResults['company'] = [
                'success' => true,
                'data' => $company,
                'message' => 'Dados da empresa obtidos com sucesso'
            ];
        } catch (Exception $e) {
            $testResults['company'] = [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erro ao obter dados da empresa'
            ];
        }
        
        // Teste 4: Simular saque crypto
        try {
            $cryptoSim = $gatewayAPI->simulateCryptoTransfer(100000, 'BTC'); // R$ 1000,00 em BTC
            $testResults['crypto'] = [
                'success' => true,
                'data' => $cryptoSim,
                'message' => 'Simulação crypto realizada com sucesso'
            ];
        } catch (Exception $e) {
            $testResults['crypto'] = [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erro na simulação crypto'
            ];
        }
        
    } catch (Exception $e) {
        $testResults['general'] = [
            'success' => false,
            'error' => $e->getMessage(),
            'message' => 'Erro geral na inicialização da API'
        ];
    }
}

$pageTitle = 'Teste de API';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/admin_header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/admin_sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-vial me-2"></i>Teste de API
                    </h1>
                </div>

                <!-- Seleção de Cliente -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>Selecionar Cliente para Teste
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8">
                                    <select class="form-select" name="user_id" required>
                                        <option value="">Selecione um cliente...</option>
                                        <?php foreach ($clients as $client): ?>
                                        <option value="<?php echo $client['id']; ?>" 
                                                <?php echo $selectedUserId == $client['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($client['username'] . ' - ' . $client['full_name']); ?>
                                            <?php if (!$client['gateway_status']): ?>
                                                (SEM CONFIGURAÇÃO)
                                            <?php endif; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-play me-2"></i>Executar Testes
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if (!empty($testResults)): ?>
                <!-- Resultados dos Testes -->
                <div class="row">
                    <!-- Teste de Saldo -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-wallet me-2"></i>Teste: Obter Saldo
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (isset($testResults['balance'])): ?>
                                    <?php if ($testResults['balance']['success']): ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <?php echo $testResults['balance']['message']; ?>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Saldo Disponível:</strong><br>
                                                R$ <?php echo formatMoney(centavosToReais($testResults['balance']['data']['amount'])); ?>
                                            </div>
                                            <div class="col-6">
                                                <strong>Aguardando:</strong><br>
                                                R$ <?php echo formatMoney(centavosToReais($testResults['balance']['data']['waitingFunds'] ?? 0)); ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <?php echo $testResults['balance']['message']; ?>
                                        </div>
                                        <small class="text-muted"><?php echo htmlspecialchars($testResults['balance']['error']); ?></small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Teste de Transações -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-credit-card me-2"></i>Teste: Listar Transações
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (isset($testResults['transactions'])): ?>
                                    <?php if ($testResults['transactions']['success']): ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <?php echo $testResults['transactions']['message']; ?>
                                        </div>
                                        <p><strong>Total encontrado:</strong> <?php echo count($testResults['transactions']['data']['data'] ?? []); ?> transações</p>
                                    <?php else: ?>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <?php echo $testResults['transactions']['message']; ?>
                                        </div>
                                        <small class="text-muted"><?php echo htmlspecialchars($testResults['transactions']['error']); ?></small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Teste de Dados da Empresa -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-building me-2"></i>Teste: Dados da Empresa
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (isset($testResults['company'])): ?>
                                    <?php if ($testResults['company']['success']): ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <?php echo $testResults['company']['message']; ?>
                                        </div>
                                        <?php if (isset($testResults['company']['data']['name'])): ?>
                                            <p><strong>Empresa:</strong> <?php echo htmlspecialchars($testResults['company']['data']['name']); ?></p>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <?php echo $testResults['company']['message']; ?>
                                        </div>
                                        <small class="text-muted"><?php echo htmlspecialchars($testResults['company']['error']); ?></small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Teste de Simulação Crypto -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-bitcoin me-2"></i>Teste: Simulação Crypto
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (isset($testResults['crypto'])): ?>
                                    <?php if ($testResults['crypto']['success']): ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <?php echo $testResults['crypto']['message']; ?>
                                        </div>
                                        <p><strong>Simulação:</strong> R$ 1.000,00 em BTC</p>
                                        <?php if (isset($testResults['crypto']['data']['expectedAmount'])): ?>
                                            <p><strong>Valor em BTC:</strong> <?php echo $testResults['crypto']['data']['expectedAmount']; ?></p>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <?php echo $testResults['crypto']['message']; ?>
                                        </div>
                                        <small class="text-muted"><?php echo htmlspecialchars($testResults['crypto']['error']); ?></small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resumo Geral -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-clipboard-check me-2"></i>Resumo dos Testes
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php
                        $totalTests = count($testResults);
                        $successfulTests = 0;
                        foreach ($testResults as $result) {
                            if ($result['success']) $successfulTests++;
                        }
                        ?>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-primary"><?php echo $totalTests; ?></h4>
                                    <small class="text-muted">Total de Testes</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-success"><?php echo $successfulTests; ?></h4>
                                    <small class="text-muted">Sucessos</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-danger"><?php echo $totalTests - $successfulTests; ?></h4>
                                    <small class="text-muted">Falhas</small>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($successfulTests === $totalTests): ?>
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Parabéns!</strong> Todos os testes foram executados com sucesso. A API está funcionando corretamente.
                            </div>
                        <?php elseif ($successfulTests > 0): ?>
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Atenção!</strong> Alguns testes falharam. Verifique as configurações do cliente.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger mt-3">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>Erro!</strong> Todos os testes falharam. Verifique as credenciais e configurações.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Informações Importantes -->
                <div class="card shadow mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i>Informações Importantes
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>Dicas para Solução de Problemas:</h6>
                            <ul class="mb-0">
                                <li><strong>Erro de autenticação:</strong> Verifique se as credenciais estão corretas</li>
                                <li><strong>Erro de conexão:</strong> Confirme se a URL da API está acessível</li>
                                <li><strong>Erro 404:</strong> Verifique se os endpoints estão corretos</li>
                                <li><strong>Credenciais de exemplo:</strong> Podem não estar ativas na API real</li>
                            </ul>
                        </div>
                        
                        <h6>Credenciais Configuradas (PagDrop):</h6>
                        <ul>
                            <li><strong>URL Base:</strong> https://api.pagdrop.com</li>
                            <li><strong>Chave Pública:</strong> pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc</li>
                            <li><strong>Chave Privada:</strong> sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S</li>
                            <li><strong>Chave de Saque:</strong> wk_EPrMXHkYHk_iB3yyVxMQ3u4jzSnWFylU_rZs4TAG3IVppC6m</li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>

2025-07-31 05:17:08 | INFO | 2 | ::1 | API_CALL: PagDrop | {"endpoint":"\/v1\/balance\/available","method":"GET","response_code":200,"response_size":85,"execution_time":0.29943108558654785}
2025-07-31 05:17:08 | INFO |  | ::1 | Sistema configurado com sucesso | {"setup_completed":true,"demo_user_configured":true}
2025-07-31 05:17:28 | INFO | 2 | ::1 | API_CALL: PagDrop | {"endpoint":"\/v1\/balance\/available","method":"GET","response_code":200,"response_size":85,"execution_time":0.1649610996246338}
2025-07-31 05:17:28 | INFO |  | ::1 | Sistema configurado com sucesso | {"setup_completed":true,"demo_user_configured":true}
2025-07-31 05:17:33 | ERROR |  | ::1 | Erro no login: Erro na consulta ao banco de dados | []
2025-07-31 05:17:41 | ERROR |  | ::1 | Erro no login: Erro na consulta ao banco de dados | []
2025-07-31 05:19:07 | ERROR |  | ::1 | Erro no login: Erro na consulta ao banco de dados | []
2025-07-31 05:19:07 | ERROR |  | ::1 | Erro no login: Erro na consulta ao banco de dados | []
2025-07-31 05:19:31 | ERROR |  | ::1 | Erro no login: Erro na consulta ao banco de dados | []

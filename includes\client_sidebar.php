<?php
$currentPage = basename($_SERVER['PHP_SELF']);
$currentDir = basename(dirname($_SERVER['PHP_SELF']));

function isActiveClient($page, $dir = 'client') {
    global $currentPage, $currentDir;
    if ($currentDir !== $dir) return '';
    return $currentPage === $page ? 'active' : '';
}
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('dashboard.php'); ?>" href="<?php echo BASE_URL; ?>client/dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Financeiro</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('balance.php'); ?>" href="<?php echo BASE_URL; ?>client/balance.php">
                    <i class="fas fa-wallet me-2"></i>
                    Saldo
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('transactions.php'); ?>" href="<?php echo BASE_URL; ?>client/transactions.php">
                    <i class="fas fa-credit-card me-2"></i>
                    Transações
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('transfers.php'); ?>" href="<?php echo BASE_URL; ?>client/transfers.php">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Saques
                </a>
            </li>
            
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Operações</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('create_transaction.php'); ?>" href="<?php echo BASE_URL; ?>client/create_transaction.php">
                    <i class="fas fa-plus me-2"></i>
                    Nova Transação
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('create_transfer.php'); ?>" href="<?php echo BASE_URL; ?>client/create_transfer.php">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    Solicitar Saque
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('simulate_crypto.php'); ?>" href="<?php echo BASE_URL; ?>client/simulate_crypto.php">
                    <i class="fas fa-bitcoin me-2"></i>
                    Simular Crypto
                </a>
            </li>
            
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Conta</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('profile.php'); ?>" href="<?php echo BASE_URL; ?>client/profile.php">
                    <i class="fas fa-user me-2"></i>
                    Meu Perfil
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('settings.php'); ?>" href="<?php echo BASE_URL; ?>client/settings.php">
                    <i class="fas fa-cog me-2"></i>
                    Configurações
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo isActiveClient('help.php'); ?>" href="<?php echo BASE_URL; ?>client/help.php">
                    <i class="fas fa-question-circle me-2"></i>
                    Ajuda & Suporte
                </a>
            </li>
        </ul>
        
        <hr class="my-3">
        
        <!-- Status da Conta -->
        <div class="px-3">
            <h6 class="sidebar-heading text-muted">
                <span>Status da Conta</span>
            </h6>
            <div class="small text-muted">
                <div class="mb-1">
                    <i class="fas fa-user me-1"></i>
                    <?php echo htmlspecialchars($user['username']); ?>
                </div>
                <div class="mb-1">
                    <i class="fas fa-shield-alt me-1"></i>
                    Status: <span class="text-success">Ativo</span>
                </div>
                <div class="mb-1">
                    <i class="fas fa-clock me-1"></i>
                    <?php echo date('d/m/Y H:i'); ?>
                </div>
            </div>
        </div>
        
        <hr class="my-3">
        
        <!-- Ações Rápidas -->
        <div class="px-3">
            <h6 class="sidebar-heading text-muted">
                <span>Ações Rápidas</span>
            </h6>
            <div class="d-grid gap-2">
                <a href="<?php echo BASE_URL; ?>client/create_transaction.php" class="btn btn-sm btn-success">
                    <i class="fas fa-plus me-1"></i>
                    Nova Transação
                </a>
                <a href="<?php echo BASE_URL; ?>client/create_transfer.php" class="btn btn-sm btn-warning">
                    <i class="fas fa-money-bill-wave me-1"></i>
                    Saque
                </a>
                <button class="btn btn-sm btn-info" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Atualizar
                </button>
            </div>
        </div>
        
        <hr class="my-3">
        
        <!-- Informações de Contato -->
        <div class="px-3">
            <h6 class="sidebar-heading text-muted">
                <span>Suporte</span>
            </h6>
            <div class="small text-muted">
                <div class="mb-1">
                    <i class="fas fa-envelope me-1"></i>
                    <a href="mailto:<?php echo ADMIN_EMAIL; ?>" class="text-decoration-none">
                        Contato
                    </a>
                </div>
                <div class="mb-1">
                    <i class="fas fa-book me-1"></i>
                    <a href="<?php echo BASE_URL; ?>client/help.php" class="text-decoration-none">
                        Documentação
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.15);
    border-right: 3px solid #007bff;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

.sidebar hr {
    margin: 1rem 0;
    border-color: rgba(0, 0, 0, 0.1);
}

.sidebar a {
    color: #6c757d;
    text-decoration: none;
}

.sidebar a:hover {
    color: #007bff;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 56px;
    }
}

/* Scrollbar personalizada */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

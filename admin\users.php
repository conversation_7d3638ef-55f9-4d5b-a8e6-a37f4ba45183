<?php
require_once '../config/config.php';

$auth = new Auth();
$auth->requireAdmin();

$db = Database::getInstance();
$logger = new Logger();

$action = $_GET['action'] ?? 'list';
$userId = $_GET['id'] ?? null;
$message = '';
$error = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Token de segurança inválido';
    } else {
        try {
            switch ($action) {
                case 'create':
                    $userData = [
                        'username' => sanitizeInput($_POST['username']),
                        'email' => sanitizeInput($_POST['email']),
                        'password' => $_POST['password'],
                        'full_name' => sanitizeInput($_POST['full_name']),
                        'user_type' => $_POST['user_type'] ?? 'client'
                    ];
                    
                    $newUserId = $auth->register($userData);
                    
                    // Criar configuração de gateway padrão se for cliente
                    if ($userData['user_type'] === 'client') {
                        $gatewayConfig = [
                            'user_id' => $newUserId,
                            'gateway_name' => 'PagDrop',
                            'api_base_url' => 'https://api.pagdrop.com',
                            'public_key' => sanitizeInput($_POST['public_key'] ?? ''),
                            'secret_key' => sanitizeInput($_POST['secret_key'] ?? ''),
                            'withdraw_key' => sanitizeInput($_POST['withdraw_key'] ?? ''),
                            'admin_fee_percentage' => floatval($_POST['admin_fee_percentage'] ?? 10.00)
                        ];
                        
                        $db->insert('gateway_configs', $gatewayConfig);
                    }
                    
                    $message = 'Usuário criado com sucesso!';
                    $action = 'list';
                    break;
                    
                case 'edit':
                    $updateData = [
                        'username' => sanitizeInput($_POST['username']),
                        'email' => sanitizeInput($_POST['email']),
                        'full_name' => sanitizeInput($_POST['full_name']),
                        'status' => $_POST['status']
                    ];
                    
                    if (!empty($_POST['password'])) {
                        $updateData['password'] = password_hash($_POST['password'], PASSWORD_HASH_ALGO);
                    }
                    
                    $db->update('users', $updateData, 'id = :id', ['id' => $userId]);
                    
                    // Atualizar configuração do gateway se for cliente
                    $user = $db->fetch('SELECT user_type FROM users WHERE id = :id', ['id' => $userId]);
                    if ($user['user_type'] === 'client') {
                        $gatewayConfig = [
                            'public_key' => sanitizeInput($_POST['public_key'] ?? ''),
                            'secret_key' => sanitizeInput($_POST['secret_key'] ?? ''),
                            'withdraw_key' => sanitizeInput($_POST['withdraw_key'] ?? ''),
                            'admin_fee_percentage' => floatval($_POST['admin_fee_percentage'] ?? 10.00)
                        ];
                        
                        $db->update('gateway_configs', $gatewayConfig, 'user_id = :user_id', ['user_id' => $userId]);
                    }
                    
                    $message = 'Usuário atualizado com sucesso!';
                    $action = 'list';
                    break;
                    
                case 'delete':
                    $db->delete('users', 'id = :id', ['id' => $userId]);
                    $message = 'Usuário excluído com sucesso!';
                    $action = 'list';
                    break;
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Obter dados conforme a ação
$users = [];
$user = null;
$gatewayConfig = null;

if ($action === 'list') {
    $users = $db->fetchAll("
        SELECT u.*, gc.admin_fee_percentage, gc.status as gateway_status
        FROM users u 
        LEFT JOIN gateway_configs gc ON u.id = gc.user_id 
        ORDER BY u.created_at DESC
    ");
} elseif (in_array($action, ['edit', 'view']) && $userId) {
    $user = $db->fetch('SELECT * FROM users WHERE id = :id', ['id' => $userId]);
    if ($user && $user['user_type'] === 'client') {
        $gatewayConfig = $db->fetch('SELECT * FROM gateway_configs WHERE user_id = :user_id', ['user_id' => $userId]);
    }
}

$pageTitle = 'Gerenciar Usuários';
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/admin_header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/admin_sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        <?php 
                        switch($action) {
                            case 'create': echo 'Criar Usuário'; break;
                            case 'edit': echo 'Editar Usuário'; break;
                            case 'view': echo 'Visualizar Usuário'; break;
                            default: echo 'Gerenciar Usuários'; break;
                        }
                        ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <?php if ($action === 'list'): ?>
                        <a href="?action=create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Novo Usuário
                        </a>
                        <?php else: ?>
                        <a href="users.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Voltar
                        </a>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                <!-- Lista de Usuários -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-list me-2"></i>Lista de Usuários
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Nome Completo</th>
                                        <th>Email</th>
                                        <th>Tipo</th>
                                        <th>Taxa Admin</th>
                                        <th>Status</th>
                                        <th>Criado em</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $userItem): ?>
                                    <tr>
                                        <td><?php echo $userItem['id']; ?></td>
                                        <td><?php echo htmlspecialchars($userItem['username']); ?></td>
                                        <td><?php echo htmlspecialchars($userItem['full_name']); ?></td>
                                        <td><?php echo htmlspecialchars($userItem['email']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $userItem['user_type'] === 'admin' ? 'danger' : 'primary'; ?>">
                                                <?php echo ucfirst($userItem['user_type']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($userItem['user_type'] === 'client'): ?>
                                                <?php echo $userItem['admin_fee_percentage'] ?? 'N/A'; ?>%
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $userItem['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo ucfirst($userItem['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($userItem['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="?action=view&id=<?php echo $userItem['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="?action=edit&id=<?php echo $userItem['id']; ?>" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($userItem['id'] != $auth->getUserId()): ?>
                                                <a href="?action=delete&id=<?php echo $userItem['id']; ?>" 
                                                   class="btn btn-sm btn-danger btn-delete"
                                                   data-message="Tem certeza que deseja excluir este usuário?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <?php elseif (in_array($action, ['create', 'edit'])): ?>
                <!-- Formulário de Usuário -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-<?php echo $action === 'create' ? 'plus' : 'edit'; ?> me-2"></i>
                            <?php echo $action === 'create' ? 'Criar Novo Usuário' : 'Editar Usuário'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" data-validate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username *</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="full_name" class="form-label">Nome Completo *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">
                                            Senha <?php echo $action === 'edit' ? '(deixe em branco para manter)' : '*'; ?>
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               <?php echo $action === 'create' ? 'required' : ''; ?>>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="user_type" class="form-label">Tipo de Usuário</label>
                                        <select class="form-select" id="user_type" name="user_type" onchange="toggleGatewayConfig()">
                                            <option value="client" <?php echo ($user['user_type'] ?? 'client') === 'client' ? 'selected' : ''; ?>>Cliente</option>
                                            <option value="admin" <?php echo ($user['user_type'] ?? '') === 'admin' ? 'selected' : ''; ?>>Administrador</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo ($user['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Ativo</option>
                                            <option value="inactive" <?php echo ($user['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inativo</option>
                                            <option value="suspended" <?php echo ($user['status'] ?? '') === 'suspended' ? 'selected' : ''; ?>>Suspenso</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Configurações do Gateway (apenas para clientes) -->
                            <div id="gateway-config" style="<?php echo ($user['user_type'] ?? 'client') === 'admin' ? 'display: none;' : ''; ?>">
                                <hr>
                                <h5><i class="fas fa-plug me-2"></i>Configurações do Gateway</h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="public_key" class="form-label">Chave Pública</label>
                                            <input type="text" class="form-control" id="public_key" name="public_key" 
                                                   value="<?php echo htmlspecialchars($gatewayConfig['public_key'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="secret_key" class="form-label">Chave Privada</label>
                                            <input type="text" class="form-control" id="secret_key" name="secret_key" 
                                                   value="<?php echo htmlspecialchars($gatewayConfig['secret_key'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="withdraw_key" class="form-label">Chave de Saque</label>
                                            <input type="text" class="form-control" id="withdraw_key" name="withdraw_key" 
                                                   value="<?php echo htmlspecialchars($gatewayConfig['withdraw_key'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="admin_fee_percentage" class="form-label">Taxa Administrativa (%)</label>
                                            <input type="number" class="form-control" id="admin_fee_percentage" name="admin_fee_percentage" 
                                                   value="<?php echo $gatewayConfig['admin_fee_percentage'] ?? '10.00'; ?>" 
                                                   step="0.01" min="0" max="100">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="users.php" class="btn btn-secondary me-md-2">Cancelar</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $action === 'create' ? 'Criar Usuário' : 'Salvar Alterações'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <?php elseif ($action === 'view' && $user): ?>
                <!-- Visualizar Usuário -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-eye me-2"></i>Detalhes do Usuário
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <th>ID:</th>
                                        <td><?php echo $user['id']; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Username:</th>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Nome Completo:</th>
                                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email:</th>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tipo:</th>
                                        <td>
                                            <span class="badge bg-<?php echo $user['user_type'] === 'admin' ? 'danger' : 'primary'; ?>">
                                                <?php echo ucfirst($user['user_type']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Status:</th>
                                        <td>
                                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo ucfirst($user['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Criado em:</th>
                                        <td><?php echo date('d/m/Y H:i:s', strtotime($user['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Atualizado em:</th>
                                        <td><?php echo date('d/m/Y H:i:s', strtotime($user['updated_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                            
                            <?php if ($user['user_type'] === 'client' && $gatewayConfig): ?>
                            <div class="col-md-6">
                                <h6><i class="fas fa-plug me-2"></i>Configurações do Gateway</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <th>Gateway:</th>
                                        <td><?php echo htmlspecialchars($gatewayConfig['gateway_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>URL Base:</th>
                                        <td><?php echo htmlspecialchars($gatewayConfig['api_base_url']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Chave Pública:</th>
                                        <td><?php echo htmlspecialchars(substr($gatewayConfig['public_key'], 0, 20) . '...'); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Taxa Admin:</th>
                                        <td><?php echo $gatewayConfig['admin_fee_percentage']; ?>%</td>
                                    </tr>
                                    <tr>
                                        <th>Status Gateway:</th>
                                        <td>
                                            <span class="badge bg-<?php echo $gatewayConfig['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                <?php echo ucfirst($gatewayConfig['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="?action=edit&id=<?php echo $user['id']; ?>" class="btn btn-warning me-md-2">
                                <i class="fas fa-edit me-2"></i>Editar
                            </a>
                            <a href="users.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Voltar
                            </a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        function toggleGatewayConfig() {
            const userType = document.getElementById('user_type').value;
            const gatewayConfig = document.getElementById('gateway-config');
            
            if (userType === 'client') {
                gatewayConfig.style.display = 'block';
            } else {
                gatewayConfig.style.display = 'none';
            }
        }
    </script>
</body>
</html>

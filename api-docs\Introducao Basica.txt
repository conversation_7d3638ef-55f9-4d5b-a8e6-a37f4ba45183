Introdução
Obrigado por acessar a documentação da nossa API! Nosso objetivo é ajudar você a integrar nossos serviços de forma prática e eficiente.

Descrição
A API foi desenvolvida seguindo os padrões REST, proporcionando uma experiência simples e compatível. Todas as respostas são fornecidas no formato JSON, garantindo uma estrutura versátil e de fácil compreensão para os desenvolvedores.

Para realizar requisições diretamente por esta documentação, utilize sua chave pública como username e sua chave secreta como password no campo de autenticação.

Autenticação
Para acessar nossa API, é necessário autenticar-se utilizando sua chave secreta. Essa autenticação segue o padrão Basic Access Authentication.

Você pode localizar sua chave secreta acessando o menu de integrações em sua conta.

Após localizar a chave, inclua-a no cabeçalho da requisição HTTP, no campo Authorization, conforme o exemplo abaixo:

NODE

try {
    const url = 'https://api.pagdrop.com/v1/transactions';
    const publicKey = 'PUBLIC_KEY';
    const secretKey = 'SECRET_KEY';
    const auth = 'Basic ' + Buffer.from(publicKey + ':' + secretKey).toString('base64');

    const payload = {
      amount: 100,
      paymentMethod: 'pix',
      // Valores do payload aqui...
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: auth,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();
    console.log(data);
  } catch (error) {
    console.error('Erro na requisição:', error);
  }
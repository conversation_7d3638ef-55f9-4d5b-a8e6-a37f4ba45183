<?php
require_once 'config/config.php';

$auth = new Auth();
$auth->requireLogin();

$user = $auth->getUser();
$isAdmin = $auth->isAdmin();

// Redirecionar cliente para painel específico
if (!$isAdmin) {
    header('Location: client/dashboard.php');
    exit;
}

$db = Database::getInstance();
$logger = new Logger();

// Obter estatísticas gerais
$stats = [
    'total_users' => $db->count('users', "user_type = 'client'"),
    'active_users' => $db->count('users', "user_type = 'client' AND status = 'active'"),
    'total_transactions' => $db->count('transactions'),
    'total_transfers' => $db->count('transfers'),
    'total_fees' => $db->fetch("SELECT SUM(fee_amount) as total FROM admin_fees WHERE pix_status = 'sent'")['total'] ?? 0
];

// Transações recentes
$recentTransactions = $db->fetchAll("
    SELECT t.*, u.username, u.full_name 
    FROM transactions t 
    JOIN users u ON t.user_id = u.id 
    ORDER BY t.created_at DESC 
    LIMIT 10
");

// Transferências recentes
$recentTransfers = $db->fetchAll("
    SELECT tr.*, u.username, u.full_name 
    FROM transfers tr 
    JOIN users u ON tr.user_id = u.id 
    ORDER BY tr.created_at DESC 
    LIMIT 10
");

$pageTitle = 'Dashboard Administrativo';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SYSTEM_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/admin_header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Atualizar
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Cards de Estatísticas -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Total de Clientes
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_users']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Clientes Ativos
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['active_users']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Total Transações
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($stats['total_transactions']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Total em Taxas
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            R$ <?php echo formatMoney($stats['total_fees']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gráficos e Tabelas -->
                <div class="row">
                    <!-- Transações Recentes -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-list me-2"></i>Transações Recentes
                                </h6>
                                <a href="admin/transactions.php" class="btn btn-sm btn-primary">Ver Todas</a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Cliente</th>
                                                <th>Valor</th>
                                                <th>Status</th>
                                                <th>Data</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentTransactions as $transaction): ?>
                                            <tr>
                                                <td>#<?php echo $transaction['id']; ?></td>
                                                <td><?php echo htmlspecialchars($transaction['username']); ?></td>
                                                <td>R$ <?php echo formatMoney($transaction['amount']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo getStatusColor($transaction['status']); ?>">
                                                        <?php echo ucfirst($transaction['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('d/m/Y H:i', strtotime($transaction['created_at'])); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transferências Recentes -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-exchange-alt me-2"></i>Transferências Recentes
                                </h6>
                                <a href="admin/transfers.php" class="btn btn-sm btn-primary">Ver Todas</a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Cliente</th>
                                                <th>Valor</th>
                                                <th>Status</th>
                                                <th>Data</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentTransfers as $transfer): ?>
                                            <tr>
                                                <td>#<?php echo $transfer['id']; ?></td>
                                                <td><?php echo htmlspecialchars($transfer['username']); ?></td>
                                                <td>R$ <?php echo formatMoney($transfer['amount']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo getStatusColor($transfer['status']); ?>">
                                                        <?php echo ucfirst($transfer['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('d/m/Y H:i', strtotime($transfer['created_at'])); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ações Rápidas -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-bolt me-2"></i>Ações Rápidas
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <a href="admin/users.php?action=create" class="btn btn-success w-100">
                                            <i class="fas fa-user-plus me-2"></i>Novo Cliente
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="admin/settings.php" class="btn btn-primary w-100">
                                            <i class="fas fa-cog me-2"></i>Configurações
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="admin/logs.php" class="btn btn-info w-100">
                                            <i class="fas fa-list-alt me-2"></i>Ver Logs
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="admin/fees.php" class="btn btn-warning w-100">
                                            <i class="fas fa-percentage me-2"></i>Relatório Taxas
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/admin.js"></script>
</body>
</html>

<?php
function getStatusColor($status) {
    $colors = [
        'paid' => 'success',
        'approved' => 'success',
        'waiting_payment' => 'warning',
        'pending' => 'warning',
        'refused' => 'danger',
        'cancelled' => 'secondary',
        'COMPLETED' => 'success',
        'PROCESSING' => 'warning',
        'PENDING_QUEUE' => 'info',
        'CANCELLED' => 'secondary',
        'REFUSED' => 'danger'
    ];
    
    return $colors[$status] ?? 'secondary';
}
?>

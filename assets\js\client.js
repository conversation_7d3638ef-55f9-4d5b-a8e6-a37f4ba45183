/**
 * JavaScript para Painel do Cliente
 * Sistema Universal de Gateways
 */

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    initializeClient();
});

/**
 * Inicializar funcionalidades do cliente
 */
function initializeClient() {
    // Inicializar tooltips
    initializeTooltips();
    
    // Inicializar formulários
    initializeForms();
    
    // Inicializar QR codes
    initializeQRCodes();
    
    // Inicializar auto-refresh
    initializeAutoRefresh();
    
    // Inicializar notificações
    initializeNotifications();
    
    // Inicializar copy to clipboard
    initializeCopyToClipboard();
}

/**
 * Inicializar tooltips do Bootstrap
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Inicializar funcionalidades de formulários
 */
function initializeForms() {
    // Validação em tempo real
    document.querySelectorAll('form[data-validate]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // Mostrar loading
            showFormLoading(this);
        });
    });
    
    // Máscaras para campos
    initializeInputMasks();
    
    // Formatação de valores monetários
    initializeMoneyInputs();
}

/**
 * Validar formulário
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    // Limpar erros anteriores
    form.querySelectorAll('.is-invalid').forEach(field => {
        field.classList.remove('is-invalid');
    });
    form.querySelectorAll('.invalid-feedback').forEach(feedback => {
        feedback.remove();
    });
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            showFieldError(field, 'Este campo é obrigatório');
            isValid = false;
        }
    });
    
    // Validações específicas
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(function(field) {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Email inválido');
            isValid = false;
        }
    });
    
    // Validar valores monetários
    const moneyFields = form.querySelectorAll('.money-input');
    moneyFields.forEach(function(field) {
        if (field.value && parseFloat(field.value.replace(/[^\d,]/g, '').replace(',', '.')) <= 0) {
            showFieldError(field, 'Valor deve ser maior que zero');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Mostrar erro em campo
 */
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * Validar email
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Mostrar loading no formulário
 */
function showFormLoading(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processando...';
        submitBtn.disabled = true;
        
        // Restaurar após 30 segundos (timeout)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 30000);
    }
}

/**
 * Inicializar máscaras de entrada
 */
function initializeInputMasks() {
    // Máscara para PIX key
    document.querySelectorAll('.pix-key-input').forEach(function(input) {
        const typeSelect = document.querySelector('select[name="pix_key_type"]');
        
        function applyMask() {
            const type = typeSelect ? typeSelect.value : 'email';
            
            switch(type) {
                case 'cpf':
                    input.value = formatCPF(input.value);
                    break;
                case 'cnpj':
                    input.value = formatCNPJ(input.value);
                    break;
                case 'phone':
                    input.value = formatPhone(input.value);
                    break;
                default:
                    // Email ou EVP - sem máscara
                    break;
            }
        }
        
        input.addEventListener('input', applyMask);
        if (typeSelect) {
            typeSelect.addEventListener('change', applyMask);
        }
    });
}

/**
 * Inicializar inputs de dinheiro
 */
function initializeMoneyInputs() {
    document.querySelectorAll('.money-input').forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = formatMoneyInput(this.value);
        });
        
        input.addEventListener('blur', function() {
            // Validar valor mínimo
            const value = parseFloat(this.value.replace(/[^\d,]/g, '').replace(',', '.'));
            if (value < 1) {
                showToast('Valor mínimo é R$ 1,00', 'warning');
                this.focus();
            }
        });
    });
}

/**
 * Formatar input de dinheiro
 */
function formatMoneyInput(value) {
    // Remove tudo que não é dígito
    value = value.replace(/\D/g, '');
    
    // Converte para centavos
    value = (value / 100).toFixed(2) + '';
    
    // Adiciona vírgula decimal
    value = value.replace('.', ',');
    
    // Adiciona pontos de milhares
    value = value.replace(/(\d)(\d{3})(\d{3}),/g, '$1.$2.$3,');
    value = value.replace(/(\d)(\d{3}),/g, '$1.$2,');
    
    return 'R$ ' + value;
}

/**
 * Formatar CPF
 */
function formatCPF(value) {
    return value
        .replace(/\D/g, '')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d{1,2})/, '$1-$2')
        .replace(/(-\d{2})\d+?$/, '$1');
}

/**
 * Formatar CNPJ
 */
function formatCNPJ(value) {
    return value
        .replace(/\D/g, '')
        .replace(/(\d{2})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1/$2')
        .replace(/(\d{4})(\d{1,2})/, '$1-$2')
        .replace(/(-\d{2})\d+?$/, '$1');
}

/**
 * Formatar telefone
 */
function formatPhone(value) {
    return value
        .replace(/\D/g, '')
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .replace(/(\d{4})-(\d)(\d{4})/, '$1$2-$3')
        .replace(/(-\d{4})\d+?$/, '$1');
}

/**
 * Inicializar QR codes
 */
function initializeQRCodes() {
    // Auto-refresh para QR codes com expiração
    document.querySelectorAll('.qr-code[data-expires]').forEach(function(qrCode) {
        const expiresAt = new Date(qrCode.getAttribute('data-expires'));
        const now = new Date();
        
        if (expiresAt > now) {
            const timeLeft = expiresAt - now;
            
            // Atualizar countdown
            updateCountdown(qrCode, expiresAt);
            
            // Atualizar a cada segundo
            const interval = setInterval(() => {
                updateCountdown(qrCode, expiresAt);
                
                if (new Date() >= expiresAt) {
                    clearInterval(interval);
                    showExpiredQRCode(qrCode);
                }
            }, 1000);
        } else {
            showExpiredQRCode(qrCode);
        }
    });
}

/**
 * Atualizar countdown do QR code
 */
function updateCountdown(qrCode, expiresAt) {
    const now = new Date();
    const timeLeft = expiresAt - now;
    
    if (timeLeft <= 0) {
        return;
    }
    
    const minutes = Math.floor(timeLeft / 60000);
    const seconds = Math.floor((timeLeft % 60000) / 1000);
    
    let countdownElement = qrCode.querySelector('.qr-countdown');
    if (!countdownElement) {
        countdownElement = document.createElement('div');
        countdownElement.className = 'qr-countdown text-center mt-2';
        qrCode.appendChild(countdownElement);
    }
    
    countdownElement.innerHTML = `
        <small class="text-muted">
            <i class="fas fa-clock me-1"></i>
            Expira em: ${minutes}:${seconds.toString().padStart(2, '0')}
        </small>
    `;
}

/**
 * Mostrar QR code expirado
 */
function showExpiredQRCode(qrCode) {
    qrCode.innerHTML = `
        <div class="text-center p-4">
            <i class="fas fa-clock text-warning" style="font-size: 3rem;"></i>
            <h5 class="mt-3">QR Code Expirado</h5>
            <p class="text-muted">Este QR code expirou. Gere um novo para continuar.</p>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-2"></i>Atualizar
            </button>
        </div>
    `;
}

/**
 * Inicializar auto-refresh
 */
function initializeAutoRefresh() {
    // Auto-refresh para páginas de status
    if (document.querySelector('[data-auto-refresh]')) {
        setInterval(() => {
            // Verificar se há mudanças no status
            checkStatusUpdates();
        }, 10000); // A cada 10 segundos
    }
}

/**
 * Verificar atualizações de status
 */
function checkStatusUpdates() {
    const statusElements = document.querySelectorAll('[data-status-check]');
    
    statusElements.forEach(element => {
        const id = element.getAttribute('data-status-check');
        const type = element.getAttribute('data-type') || 'transaction';
        
        fetch(`../api/check_status.php?type=${type}&id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.status !== element.getAttribute('data-current-status')) {
                    // Status mudou, recarregar página
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Erro ao verificar status:', error);
            });
    });
}

/**
 * Inicializar copy to clipboard
 */
function initializeCopyToClipboard() {
    document.querySelectorAll('.copy-btn').forEach(button => {
        button.addEventListener('click', function() {
            const target = this.getAttribute('data-copy-target');
            const text = this.getAttribute('data-copy-text') || 
                        document.querySelector(target)?.textContent ||
                        document.querySelector(target)?.value;
            
            if (text) {
                copyToClipboard(text);
                showToast('Copiado para a área de transferência!', 'success');
                
                // Feedback visual
                const originalIcon = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    this.innerHTML = originalIcon;
                }, 2000);
            }
        });
    });
}

/**
 * Copiar para clipboard
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
    } else {
        // Fallback para navegadores antigos
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

/**
 * Inicializar sistema de notificações
 */
function initializeNotifications() {
    // Auto-hide para alertas
    document.querySelectorAll('.alert[data-auto-hide]').forEach(function(alert) {
        const delay = parseInt(alert.getAttribute('data-auto-hide')) * 1000;
        
        setTimeout(function() {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            
            setTimeout(function() {
                alert.remove();
            }, 500);
        }, delay);
    });
}

/**
 * Mostrar notificação toast
 */
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remover após esconder
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

/**
 * Criar container de toasts
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1055';
    
    document.body.appendChild(container);
    return container;
}

/**
 * Simular saque crypto
 */
function simulateCrypto() {
    const amount = document.getElementById('crypto-amount').value;
    const coin = document.getElementById('crypto-coin').value;
    
    if (!amount || !coin) {
        showToast('Preencha todos os campos', 'warning');
        return;
    }
    
    const resultDiv = document.getElementById('simulation-result');
    resultDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Simulando...</div>';
    
    fetch('../api/simulate_crypto.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            amount: parseFloat(amount.replace(/[^\d,]/g, '').replace(',', '.')) * 100,
            coin: coin
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Simulação de Saque ${data.data.coin}</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Valor em BRL:</strong> R$ ${formatMoney(data.data.amount / 100)}</p>
                                <p><strong>Valor em ${data.data.coin}:</strong> ${data.data.expectedAmount}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Cotação:</strong> R$ ${formatMoney(data.data.price / 100)}</p>
                                <p><strong>Redes disponíveis:</strong></p>
                                <ul>
                                    ${data.data.availableNetworks.map(network => 
                                        `<li>${network.network.toUpperCase()} - Taxa: ${network.networkFee}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger">Erro na simulação</div>';
        console.error('Erro:', error);
    });
}

/**
 * Formatar valor monetário
 */
function formatMoney(value) {
    return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value);
}

/**
 * Utilitários globais
 */
window.ClientUtils = {
    showToast: showToast,
    validateForm: validateForm,
    formatCPF: formatCPF,
    formatCNPJ: formatCNPJ,
    formatPhone: formatPhone,
    formatMoney: formatMoney,
    copyToClipboard: copyToClipboard,
    simulateCrypto: simulateCrypto
};

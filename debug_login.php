<?php
/**
 * Debug do sistema de login
 */

require_once 'config/config.php';

echo "<h1>Debug do Sistema de Login</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. Teste de Conexão com Banco</h2>";
    $testQuery = $db->fetch("SELECT 1 as test");
    echo $testQuery ? "✅ Conexão OK" : "❌ Falha na conexão";
    
    echo "<h2>2. Verificar Usuários no Banco</h2>";
    $users = $db->fetchAll("SELECT id, username, email, password, status, user_type FROM users");
    
    if (empty($users)) {
        echo "<p style='color: red;'>❌ Nenhum usuário encontrado! Execute o database.sql primeiro.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Status</th><th>Tipo</th><th>Hash</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "<td>" . $user['user_type'] . "</td>";
            echo "<td>" . substr($user['password'], 0, 20) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Teste de Hash de Senha</h2>";
    $testPassword = 'password';
    $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
    
    echo "<p><strong>Senha teste:</strong> " . $testPassword . "</p>";
    echo "<p><strong>Hash gerado:</strong> " . $newHash . "</p>";
    echo "<p><strong>Verificação:</strong> " . (password_verify($testPassword, $newHash) ? "✅ OK" : "❌ FALHA") . "</p>";
    
    echo "<h2>4. Verificar Senhas dos Usuários</h2>";
    foreach ($users as $user) {
        $isValid = password_verify('password', $user['password']);
        echo "<p><strong>" . $user['username'] . ":</strong> ";
        echo "<span style='color: " . ($isValid ? 'green' : 'red') . ";'>";
        echo $isValid ? "✅ Senha OK" : "❌ Senha inválida";
        echo "</span></p>";
        
        if (!$isValid) {
            echo "<p style='margin-left: 20px; color: orange;'>Atualizando senha...</p>";
            $db->update('users', 
                ['password' => $newHash], 
                'id = :id', 
                ['id' => $user['id']]
            );
            echo "<p style='margin-left: 20px; color: green;'>✅ Senha atualizada!</p>";
        }
    }
    
    echo "<h2>5. Teste de Login Direto</h2>";
    
    // Teste manual do processo de login
    $username = 'admin';
    $password = 'password';
    
    echo "<p>Testando login: <strong>{$username}</strong> / <strong>{$password}</strong></p>";
    
    // Buscar usuário
    $user = $db->fetch(
        "SELECT * FROM users WHERE (username = :username OR email = :username) AND status = 'active'",
        ['username' => $username]
    );
    
    if (!$user) {
        echo "<p style='color: red;'>❌ Usuário não encontrado ou inativo</p>";
    } else {
        echo "<p style='color: green;'>✅ Usuário encontrado: " . $user['username'] . "</p>";
        
        $passwordCheck = password_verify($password, $user['password']);
        echo "<p>Verificação de senha: " . ($passwordCheck ? "✅ OK" : "❌ FALHA") . "</p>";
        
        if ($passwordCheck) {
            echo "<p style='color: green;'><strong>✅ Login deveria funcionar!</strong></p>";
        } else {
            echo "<p style='color: red;'><strong>❌ Problema na verificação da senha</strong></p>";
        }
    }
    
    echo "<h2>6. Teste com Classe Auth</h2>";
    
    try {
        $auth = new Auth();
        
        // Limpar sessão primeiro
        session_destroy();
        session_start();
        
        $loginResult = $auth->login('admin', 'password');
        
        if ($loginResult) {
            echo "<p style='color: green;'>✅ Login com classe Auth funcionou!</p>";
            echo "<p>Usuário logado: " . ($auth->isLoggedIn() ? "Sim" : "Não") . "</p>";
            echo "<p>Tipo: " . ($auth->isAdmin() ? "Admin" : ($auth->isClient() ? "Cliente" : "Desconhecido")) . "</p>";
            $auth->logout();
        } else {
            echo "<p style='color: red;'>❌ Falha no login com classe Auth</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erro na classe Auth: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>7. Informações do Sistema</h2>";
    echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
    echo "<p><strong>Password Default:</strong> " . PASSWORD_DEFAULT . "</p>";
    echo "<p><strong>Session Status:</strong> " . session_status() . "</p>";
    echo "<p><strong>Session Name:</strong> " . session_name() . "</p>";
    
    echo "<hr>";
    echo "<h2>✅ Credenciais para Teste:</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Admin:</strong> admin / password</p>";
    echo "<p><strong>Cliente:</strong> cliente1 / password</p>";
    echo "<p><strong>URL Login:</strong> <a href='login.php'>login.php</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro geral: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

table {
    margin: 10px 0;
    width: 100%;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>

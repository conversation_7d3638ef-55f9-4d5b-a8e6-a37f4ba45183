<?php
/**
 * Classe de Conexão com Banco de Dados
 * Sistema Universal de Gateways
 */

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw new Exception("Erro de conexão com o banco de dados");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query error: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Erro na consulta ao banco de dados");
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }
    
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as total FROM {$table} WHERE {$where}";
        $result = $this->fetch($sql, $params);
        return (int) $result['total'];
    }
    
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    // Método para executar múltiplas queries (útil para instalação)
    public function executeMultiple($sql) {
        try {
            $this->connection->exec($sql);
            return true;
        } catch (PDOException $e) {
            error_log("Multiple query execution error: " . $e->getMessage());
            return false;
        }
    }
    
    // Método para verificar se uma tabela existe
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->fetch($sql, ['table' => $tableName]);
        return !empty($result);
    }
    
    // Método para obter informações da tabela
    public function getTableInfo($tableName) {
        $sql = "DESCRIBE {$tableName}";
        return $this->fetchAll($sql);
    }
    
    // Método para backup de dados
    public function backup($tables = []) {
        // Implementação básica de backup
        // Em produção, usar ferramentas específicas como mysqldump
        $backup = "-- Backup gerado em " . date('Y-m-d H:i:s') . "\n\n";
        
        if (empty($tables)) {
            $tables = $this->fetchAll("SHOW TABLES");
            $tables = array_column($tables, 'Tables_in_' . DB_NAME);
        }
        
        foreach ($tables as $table) {
            $backup .= "-- Estrutura da tabela {$table}\n";
            $createTable = $this->fetch("SHOW CREATE TABLE {$table}");
            $backup .= $createTable['Create Table'] . ";\n\n";
            
            $backup .= "-- Dados da tabela {$table}\n";
            $rows = $this->fetchAll("SELECT * FROM {$table}");
            
            foreach ($rows as $row) {
                $values = array_map(function($value) {
                    return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                }, array_values($row));
                
                $backup .= "INSERT INTO {$table} VALUES (" . implode(', ', $values) . ");\n";
            }
            $backup .= "\n";
        }
        
        return $backup;
    }
    
    public function __destruct() {
        $this->connection = null;
    }
}
?>

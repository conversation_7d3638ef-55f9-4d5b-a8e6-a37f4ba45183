<?php
/**
 * API para simular saque crypto
 * Sistema Universal de Gateways
 */

require_once '../config/config.php';

header('Content-Type: application/json');

$auth = new Auth();

// Verificar se está logado
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit;
}

// Verificar se é cliente
if (!$auth->isClient()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acesso negado']);
    exit;
}

// Obter dados do POST
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['amount']) || !isset($input['coin'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dados inválidos']);
    exit;
}

try {
    $userId = $auth->getUserId();
    $amount = (int) $input['amount']; // Valor em centavos
    $coin = strtoupper($input['coin']);
    
    // Validar valor mínimo
    if ($amount < 100) { // R$ 1,00
        echo json_encode(['success' => false, 'message' => 'Valor mínimo é R$ 1,00']);
        exit;
    }
    
    // Validar moeda
    $allowedCoins = ['BTC', 'ETH', 'USDT'];
    if (!in_array($coin, $allowedCoins)) {
        echo json_encode(['success' => false, 'message' => 'Moeda não suportada']);
        exit;
    }
    
    // Inicializar API do gateway
    $gatewayAPI = new GatewayAPI($userId);
    
    // Simular saque crypto
    $simulation = $gatewayAPI->simulateCryptoTransfer($amount, $coin);
    
    echo json_encode([
        'success' => true,
        'data' => $simulation
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro na simulação',
        'error' => $e->getMessage()
    ]);
}
?>

<?php
/**
 * Classe Universal para Integração com Gateways de Pagamento
 * Sistema Universal de Gateways
 */

class GatewayAPI {
    private $db;
    private $logger;
    private $config;
    
    public function __construct($userId) {
        $this->db = Database::getInstance();
        $this->logger = new Logger();
        $this->loadConfig($userId);
    }
    
    /**
     * Carregar configuração do gateway para o usuário
     */
    private function loadConfig($userId) {
        $this->config = $this->db->fetch(
            "SELECT * FROM gateway_configs WHERE user_id = :user_id AND status = 'active'",
            ['user_id' => $userId]
        );
        
        if (!$this->config) {
            throw new Exception('Configuração de gateway não encontrada para este usuário');
        }
    }
    
    /**
     * Fazer requisição HTTP para a API
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null, $headers = []) {
        $url = rtrim($this->config['api_base_url'], '/') . '/' . ltrim($endpoint, '/');
        
        // Headers padrão
        $defaultHeaders = [
            'Accept: application/json',
            'Content-Type: application/json',
            'User-Agent: ' . API_USER_AGENT
        ];
        
        // Autenticação Basic
        $auth = base64_encode($this->config['public_key'] . ':' . $this->config['secret_key']);
        $defaultHeaders[] = 'Authorization: Basic ' . $auth;
        
        // Adicionar header de saque se necessário
        if (!empty($this->config['withdraw_key']) && strpos($endpoint, 'transfer') !== false) {
            $defaultHeaders[] = 'x-withdraw-key: ' . $this->config['withdraw_key'];
        }
        
        // Mesclar headers
        $allHeaders = array_merge($defaultHeaders, $headers);
        
        // Configurar cURL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => API_TIMEOUT,
            CURLOPT_HTTPHEADER => $allHeaders,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        // Configurar método e dados
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }
        
        // Executar requisição
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $executionTime = microtime(true) - $startTime;
        
        // Obter informações da resposta
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // Log da requisição
        $this->logger->logAPICall($this->config['gateway_name'], $endpoint, $method, [
            'http_code' => $httpCode,
            'body' => $response,
            'execution_time' => $executionTime,
            'error' => $error
        ], $this->config['user_id']);
        
        // Verificar erros
        if ($error) {
            throw new Exception("Erro na requisição: {$error}");
        }
        
        if ($httpCode >= 400) {
            $errorData = json_decode($response, true);
            $errorMessage = $errorData['message'] ?? "Erro HTTP {$httpCode}";
            throw new Exception("API Error: {$errorMessage}");
        }
        
        return [
            'http_code' => $httpCode,
            'body' => $response,
            'data' => json_decode($response, true),
            'execution_time' => $executionTime
        ];
    }
    
    /**
     * Obter saldo disponível
     */
    public function getBalance() {
        try {
            $response = $this->makeRequest('/v1/balance/available', 'GET');
            return $response['data'];
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao obter saldo', ['error' => $e->getMessage()], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Criar transação/venda
     */
    public function createTransaction($data) {
        try {
            // Validar dados obrigatórios
            $this->validateTransactionData($data);
            
            $response = $this->makeRequest('/v1/transactions', 'POST', $data);
            
            if ($response['http_code'] === 200 || $response['http_code'] === 201) {
                $transactionData = $response['data'];
                
                // Salvar transação no banco local
                $localTransactionId = $this->saveTransaction($transactionData, $data);
                
                $this->logger->logTransaction('created', $localTransactionId, [
                    'gateway_id' => $transactionData['id'],
                    'amount' => $transactionData['amount'],
                    'payment_method' => $transactionData['paymentMethod']
                ], $this->config['user_id']);
                
                return [
                    'success' => true,
                    'data' => $transactionData,
                    'local_id' => $localTransactionId
                ];
            }
            
            throw new Exception('Falha ao criar transação');
            
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao criar transação', ['error' => $e->getMessage()], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Listar transações
     */
    public function getTransactions($page = 1, $pageSize = 20) {
        try {
            $params = "?page={$page}&pageSize={$pageSize}";
            $response = $this->makeRequest('/v1/transactions' . $params, 'GET');
            return $response['data'];
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao listar transações', ['error' => $e->getMessage()], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Buscar transação específica
     */
    public function getTransaction($transactionId) {
        try {
            $response = $this->makeRequest("/v1/transactions/{$transactionId}", 'GET');
            return $response['data'];
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao buscar transação', ['error' => $e->getMessage(), 'transaction_id' => $transactionId], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Criar saque/transferência
     */
    public function createTransfer($data) {
        try {
            // Validar dados obrigatórios
            $this->validateTransferData($data);
            
            $response = $this->makeRequest('/v1/transfers', 'POST', $data);
            
            if ($response['http_code'] === 200 || $response['http_code'] === 201) {
                $transferData = $response['data'];
                
                // Salvar transferência no banco local
                $localTransferId = $this->saveTransfer($transferData, $data);
                
                $this->logger->logTransfer('created', $localTransferId, [
                    'gateway_id' => $transferData['id'],
                    'amount' => $transferData['amount'],
                    'method' => $transferData['method']
                ], $this->config['user_id']);
                
                return [
                    'success' => true,
                    'data' => $transferData,
                    'local_id' => $localTransferId
                ];
            }
            
            throw new Exception('Falha ao criar transferência');
            
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao criar transferência', ['error' => $e->getMessage()], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Listar transferências
     */
    public function getTransfers($page = 1, $pageSize = 20) {
        try {
            $params = "?page={$page}&pageSize={$pageSize}";
            $response = $this->makeRequest('/v1/transfers' . $params, 'GET');
            return $response['data'];
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao listar transferências', ['error' => $e->getMessage()], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Simular saque crypto
     */
    public function simulateCryptoTransfer($amount, $coin) {
        try {
            $data = [
                'amount' => $amount,
                'coin' => $coin
            ];
            
            $response = $this->makeRequest('/v1/transfers/simulate', 'POST', $data);
            return $response['data'];
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao simular saque crypto', ['error' => $e->getMessage()], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Obter dados da empresa
     */
    public function getCompanyData() {
        try {
            $response = $this->makeRequest('/v1/company', 'GET');
            return $response['data'];
        } catch (Exception $e) {
            $this->logger->log('error', 'Erro ao obter dados da empresa', ['error' => $e->getMessage()], $this->config['user_id']);
            throw $e;
        }
    }
    
    /**
     * Validar dados da transação
     */
    private function validateTransactionData($data) {
        $required = ['amount', 'paymentMethod', 'items', 'customer'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Campo obrigatório: {$field}");
            }
        }
        
        // Validar customer
        if (!isset($data['customer']['name']) || !isset($data['customer']['email']) || !isset($data['customer']['document'])) {
            throw new Exception('Dados do cliente incompletos');
        }
        
        // Validar items
        if (!is_array($data['items']) || empty($data['items'])) {
            throw new Exception('Pelo menos um item é obrigatório');
        }
    }
    
    /**
     * Validar dados da transferência
     */
    private function validateTransferData($data) {
        $required = ['method', 'amount'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Campo obrigatório: {$field}");
            }
        }
        
        if ($data['method'] === 'fiat') {
            if (!isset($data['pixKey']) || !isset($data['pixKeyType'])) {
                throw new Exception('Chave PIX é obrigatória para transferências fiat');
            }
        }
    }
    
    /**
     * Salvar transação no banco local
     */
    private function saveTransaction($gatewayData, $originalData) {
        $transactionData = [
            'user_id' => $this->config['user_id'],
            'gateway_transaction_id' => $gatewayData['id'],
            'amount' => centavosToReais($gatewayData['amount']),
            'currency' => $gatewayData['currency'] ?? 'BRL',
            'payment_method' => $gatewayData['paymentMethod'],
            'status' => $gatewayData['status'],
            'customer_name' => $originalData['customer']['name'] ?? null,
            'customer_email' => $originalData['customer']['email'] ?? null,
            'customer_document' => $originalData['customer']['document']['number'] ?? null,
            'gateway_response' => json_encode($gatewayData),
            'pix_qrcode' => $gatewayData['pix']['qrcode'] ?? null,
            'pix_expiration' => isset($gatewayData['pix']['expirationDate']) ? $gatewayData['pix']['expirationDate'] : null
        ];
        
        return $this->db->insert('transactions', $transactionData);
    }
    
    /**
     * Salvar transferência no banco local
     */
    private function saveTransfer($gatewayData, $originalData) {
        $transferData = [
            'user_id' => $this->config['user_id'],
            'gateway_transfer_id' => $gatewayData['id'],
            'amount' => centavosToReais($gatewayData['amount']),
            'net_amount' => centavosToReais($gatewayData['netAmount'] ?? $gatewayData['amount']),
            'fee' => centavosToReais($gatewayData['fee'] ?? 0),
            'currency' => $gatewayData['currency'] ?? 'BRL',
            'method' => $gatewayData['method'],
            'status' => $gatewayData['status'],
            'pix_key' => $gatewayData['pixKey'] ?? null,
            'pix_key_type' => $gatewayData['pixKeyType'] ?? null,
            'crypto_wallet' => $gatewayData['cryptoWallet'] ?? null,
            'crypto_network' => $gatewayData['cryptoNetwork'] ?? null,
            'crypto_address' => $gatewayData['cryptoAddress'] ?? null,
            'description' => $gatewayData['description'] ?? null,
            'gateway_response' => json_encode($gatewayData)
        ];
        
        return $this->db->insert('transfers', $transferData);
    }
}
?>

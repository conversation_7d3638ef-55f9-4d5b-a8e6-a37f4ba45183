Obter Saldo

GET
https://api.pagdrop.com/v1/balance/available

Utilize a rota /balance/available para obter seu saldo disponível.

CREDENCIAIS
Basic
sk_YiV2gK3hlgn_X1qXK1C3u9XzpL8hRe_PTT4crQ1vm_bVa51S
:
pk_wocE7GjkRNubWDPLq3BMmurrnucOnrcTBl9aVO5PCfkR9GJc


<?php

$curl = curl_init();

curl_setopt_array($curl, [
  CURLOPT_URL => "https://api.pagdrop.com/v1/balance/available",
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => "",
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 30,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => "GET",
  CURLOPT_HTTPHEADER => [
    "accept: application/json",
    "authorization: Basic c2tfWWlWMmdLM2hsZ25fWDFxWEsxQzN1OVh6cEw4aFJlX1BUVDRjclExdm1fYlZhNTFTOnBrX3dvY0U3R2prUk51YldEUExxM0JNbXVycm51Y09ucmNUQmw5YVZPNVBDZmtSOUdKYw=="
  ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);

curl_close($curl);

if ($err) {
  echo "cURL Error #:" . $err;
} else {
  echo $response;
}


RESPONSE REAL DA API
200

{
  "amount": 1545,
  "waitingFunds": 0,
  "maxAntecipable": 0,
  "reserve": 190,
  "recipientId": 17696
}

INFORMACAO RESPONSES 

200
Objeto de resposta em caso de sucesso na consulta de saldo

400
Objeto de resposta em caso de falha

